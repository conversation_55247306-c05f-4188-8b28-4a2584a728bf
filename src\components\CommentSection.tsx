import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Comment } from "@/types/user";
import { Heart } from "lucide-react";

interface CommentSectionProps {
  productId: string;
}

const CommentSection = ({ productId }: CommentSectionProps) => {
  const [comments, setComments] = useState<Comment[]>([
    {
      id: "1",
      productId,
      userId: "user1",
      userName: "Aminata Diallo",
      userAvatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=40&h=40&fit=crop&crop=face",
      content: "Excellent produit ! J'ai hâte de voir comment cela va évoluer dans l'écosystème fintech africain.",
      createdAt: "2024-01-15T10:30:00Z",
      likes: 5,
      hasLiked: false
    },
    {
      id: "2", 
      productId,
      userId: "user2",
      userName: "Koffi Asante",
      userAvatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face",
      content: "Très prometteur ! La solution semble bien adaptée aux réalités du marché local.",
      createdAt: "2024-01-15T09:15:00Z",
      likes: 3,
      hasLiked: true
    }
  ]);

  const [newComment, setNewComment] = useState("");

  const handleSubmitComment = () => {
    if (!newComment.trim()) return;

    const comment: Comment = {
      id: Date.now().toString(),
      productId,
      userId: "current-user",
      userName: "Vous",
      userAvatar: "https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=40&h=40&fit=crop&crop=face",
      content: newComment,
      createdAt: new Date().toISOString(),
      likes: 0,
      hasLiked: false
    };

    setComments(prev => [comment, ...prev]);
    setNewComment("");
  };

  const handleLikeComment = (commentId: string) => {
    setComments(prev => 
      prev.map(comment => 
        comment.id === commentId 
          ? { 
              ...comment, 
              likes: comment.hasLiked ? comment.likes - 1 : comment.likes + 1,
              hasLiked: !comment.hasLiked 
            }
          : comment
      )
    );
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return "Il y a quelques minutes";
    if (diffInHours < 24) return `Il y a ${diffInHours}h`;
    return `Il y a ${Math.floor(diffInHours / 24)}j`;
  };

  return (
    <div className="space-y-6">
      {/* Comment Form */}
      <Card>
        <CardContent className="pt-6">
          <div className="space-y-4">
            <Textarea
              placeholder="Partagez votre avis sur ce produit..."
              value={newComment}
              onChange={(e) => setNewComment(e.target.value)}
              rows={3}
            />
            <Button onClick={handleSubmitComment} disabled={!newComment.trim()}>
              Publier un commentaire
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Comments List */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">
          Commentaires ({comments.length})
        </h3>
        
        {comments.map((comment) => (
          <Card key={comment.id} className="border-border/50">
            <CardContent className="pt-4">
              <div className="flex items-start space-x-3">
                <Avatar className="w-10 h-10">
                  <AvatarImage src={comment.userAvatar} />
                  <AvatarFallback>{comment.userName[0]}</AvatarFallback>
                </Avatar>
                
                <div className="flex-1 space-y-2">
                  <div className="flex items-center space-x-2">
                    <span className="font-medium text-sm">{comment.userName}</span>
                    <span className="text-xs text-muted-foreground">
                      {formatTimeAgo(comment.createdAt)}
                    </span>
                  </div>
                  
                  <p className="text-sm text-foreground">{comment.content}</p>
                  
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleLikeComment(comment.id)}
                    className={`text-xs h-auto p-1 ${comment.hasLiked ? 'text-red-500' : 'text-muted-foreground'}`}
                  >
                    <Heart className={`w-3 h-3 mr-1 ${comment.hasLiked ? 'fill-current' : ''}`} />
                    {comment.likes}
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default CommentSection;