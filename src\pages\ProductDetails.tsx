
import { useState, useEffect } from "react";
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import { ArrowLeft, Heart, MessageCircle, ExternalLink, Calendar, User, Tag } from "lucide-react";

interface Product {
  id: string;
  name: string;
  description: string;
  logo_url: string;
  website_url: string;
  country: string;
  category: string;
  status: string;
  votes: number;
  tags: string[];
  created_at: string;
  user_id: string;
  profiles: { full_name: string; email: string } | null;
}

interface Comment {
  id: string;
  content: string;
  likes: number;
  created_at: string;
  user_id: string;
  profiles: { full_name: string; email: string } | null;
  hasLiked?: boolean;
}

const ProductDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { user } = useAuth();
  const { toast } = useToast();
  
  const [product, setProduct] = useState<Product | null>(null);
  const [comments, setComments] = useState<Comment[]>([]);
  const [newComment, setNewComment] = useState("");
  const [loading, setLoading] = useState(true);
  const [submittingComment, setSubmittingComment] = useState(false);

  useEffect(() => {
    if (id) {
      loadProduct();
      loadComments();
    }
  }, [id]);

  const loadProduct = async () => {
    try {
      // Load product data
      const { data: productData, error: productError } = await supabase
        .from('products')
        .select('*')
        .eq('id', id)
        .single();

      if (productError) {
        console.error('Product error:', productError);
        throw productError;
      }

      if (!productData) {
        throw new Error('Product not found');
      }

      // Try to load profile data separately
      let profileData = null;
      try {
        const { data: profile } = await supabase
          .from('profiles')
          .select('full_name, email')
          .eq('id', productData.user_id)
          .single();

        profileData = profile;
      } catch (profileError) {
        console.warn('Could not load profile for product:', productData.id);
        // Continue without profile data
      }

      setProduct({
        ...productData,
        profiles: profileData
      });
    } catch (error) {
      console.error('Error loading product:', error);
      toast({
        variant: "destructive",
        title: "Erreur",
        description: "Impossible de charger le produit"
      });
    } finally {
      setLoading(false);
    }
  };

  const loadComments = async () => {
    try {
      // Load comments first
      const { data: commentsData, error: commentsError } = await supabase
        .from('product_comments')
        .select('*')
        .eq('product_id', id)
        .order('created_at', { ascending: false });

      if (commentsError) {
        console.error('Comments error:', commentsError);
        throw commentsError;
      }

      if (commentsData && commentsData.length > 0) {
        // Load profiles separately for each comment
        const commentsWithProfiles = await Promise.all(
          commentsData.map(async (comment) => {
            let profileData = null;
            try {
              const { data: profile } = await supabase
                .from('profiles')
                .select('full_name, email')
                .eq('id', comment.user_id)
                .single();

              profileData = profile;
            } catch (profileError) {
              console.warn('Could not load profile for comment:', comment.id);
              // Continue without profile data
            }

            return {
              ...comment,
              profiles: profileData,
              hasLiked: false
            };
          })
        );

        // Charger les likes pour chaque commentaire si l'utilisateur est connecté
        let finalComments = commentsWithProfiles;
        if (user) {
          const commentIds = finalComments.map(c => c.id);
          const { data: likes } = await supabase
            .from('comment_likes')
            .select('comment_id')
            .eq('user_id', user.id)
            .in('comment_id', commentIds);

          const likedCommentIds = new Set(likes?.map(l => l.comment_id));
          finalComments = finalComments.map(comment => ({
            ...comment,
            hasLiked: likedCommentIds.has(comment.id)
          }));
        }

        setComments(finalComments);
      }
    } catch (error) {
      console.error('Error loading comments:', error);
    }
  };

  const handleSubmitComment = async () => {
    if (!user) {
      toast({
        variant: "destructive",
        title: "Connexion requise",
        description: "Vous devez être connecté pour commenter"
      });
      return;
    }

    if (!newComment.trim()) {
      toast({
        variant: "destructive",
        title: "Commentaire vide",
        description: "Veuillez saisir un commentaire"
      });
      return;
    }

    setSubmittingComment(true);
    try {
      const { error } = await supabase
        .from('product_comments')
        .insert({
          product_id: id,
          user_id: user.id,
          content: newComment.trim()
        });

      if (error) throw error;

      setNewComment("");
      await loadComments();
      toast({
        title: "Commentaire ajouté",
        description: "Votre commentaire a été publié avec succès"
      });
    } catch (error) {
      console.error('Error submitting comment:', error);
      toast({
        variant: "destructive",
        title: "Erreur",
        description: "Impossible d'ajouter le commentaire"
      });
    } finally {
      setSubmittingComment(false);
    }
  };

  const handleLikeComment = async (commentId: string, hasLiked: boolean) => {
    if (!user) {
      toast({
        variant: "destructive",
        title: "Connexion requise",
        description: "Vous devez être connecté pour aimer un commentaire"
      });
      return;
    }

    try {
      if (hasLiked) {
        // Retirer le like
        await supabase
          .from('comment_likes')
          .delete()
          .eq('comment_id', commentId)
          .eq('user_id', user.id);
      } else {
        // Ajouter le like
        await supabase
          .from('comment_likes')
          .insert({
            comment_id: commentId,
            user_id: user.id
          });
      }

      await loadComments();
    } catch (error) {
      console.error('Error liking comment:', error);
      toast({
        variant: "destructive",
        title: "Erreur",
        description: "Impossible de traiter votre réaction"
      });
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-muted-foreground">Chargement...</p>
        </div>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="min-h-screen bg-background">
        <Header />
        <main className="py-16">
          <div className="container mx-auto px-4 text-center">
            <h1 className="text-2xl font-bold mb-4">Produit non trouvé</h1>
            <Button onClick={() => navigate('/')}>
              <ArrowLeft className="w-4 h-4 mr-2" />
              Retour à l'accueil
            </Button>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <Header />
      <main className="py-16">
        <div className="container mx-auto px-4 max-w-4xl">
          <Button 
            variant="ghost" 
            onClick={() => navigate('/')}
            className="mb-6"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Retour
          </Button>

          {/* En-tête du produit */}
          <div className="mb-8">
            <div className="flex items-start space-x-6">
              <img 
                src={product.logo_url} 
                alt={`${product.name} logo`}
                className="w-24 h-24 rounded-xl object-cover border border-border"
              />
              <div className="flex-1">
                <h1 className="text-3xl font-bold text-foreground mb-2">{product.name}</h1>
                <p className="text-lg text-muted-foreground mb-4">{product.description}</p>
                
                <div className="flex flex-wrap gap-2 mb-4">
                  <Badge variant="secondary" className="flex items-center gap-1">
                    <User className="w-3 h-3" />
                    {product.profiles?.full_name || 'Utilisateur'}
                  </Badge>
                  <Badge variant="outline" className="flex items-center gap-1">
                    <Calendar className="w-3 h-3" />
                    {new Date(product.created_at).toLocaleDateString('fr-FR')}
                  </Badge>
                  <Badge variant="outline">📍 {product.country}</Badge>
                  <Badge variant="outline">{product.status}</Badge>
                  <Badge variant="outline">{product.category}</Badge>
                </div>

                <div className="flex flex-wrap gap-1 mb-4">
                  {product.tags.map((tag, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      <Tag className="w-3 h-3 mr-1" />
                      {tag}
                    </Badge>
                  ))}
                </div>

                <div className="flex items-center space-x-4">
                  <Button onClick={() => window.open(product.website_url, '_blank')}>
                    <ExternalLink className="w-4 h-4 mr-2" />
                    Visiter le site
                  </Button>
                  <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                    <Heart className="w-4 h-4" />
                    <span>{product.votes} votes</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <Separator className="mb-8" />

          {/* Section commentaires */}
          <div className="space-y-6">
            <div className="flex items-center space-x-2">
              <MessageCircle className="w-5 h-5" />
              <h2 className="text-xl font-semibold">Commentaires ({comments.length})</h2>
            </div>

            {/* Formulaire de nouveau commentaire */}
            {user ? (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Ajouter un commentaire</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Textarea
                    placeholder="Partagez votre avis sur ce produit..."
                    value={newComment}
                    onChange={(e) => setNewComment(e.target.value)}
                    className="min-h-[100px]"
                  />
                  <Button 
                    onClick={handleSubmitComment}
                    disabled={submittingComment || !newComment.trim()}
                  >
                    {submittingComment ? "Publication..." : "Publier le commentaire"}
                  </Button>
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardContent className="py-6 text-center">
                  <p className="text-muted-foreground mb-4">
                    Connectez-vous pour laisser un commentaire
                  </p>
                  <Button onClick={() => navigate('/auth')}>
                    Se connecter
                  </Button>
                </CardContent>
              </Card>
            )}

            {/* Liste des commentaires */}
            <div className="space-y-4">
              {comments.map((comment) => (
                <Card key={comment.id}>
                  <CardContent className="py-4">
                    <div className="flex items-start space-x-3">
                      <Avatar>
                        <AvatarFallback>
                          {comment.profiles?.full_name?.charAt(0)?.toUpperCase() || 'U'}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <span className="font-medium">{comment.profiles?.full_name || 'Utilisateur'}</span>
                          <span className="text-sm text-muted-foreground">
                            {new Date(comment.created_at).toLocaleDateString('fr-FR')}
                          </span>
                        </div>
                        <p className="text-foreground mb-3">{comment.content}</p>
                        <div className="flex items-center space-x-4">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleLikeComment(comment.id, comment.hasLiked || false)}
                            className={comment.hasLiked ? "text-red-500" : ""}
                          >
                            <Heart className={`w-4 h-4 mr-1 ${comment.hasLiked ? 'fill-current' : ''}`} />
                            {comment.likes}
                          </Button>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {comments.length === 0 && (
              <Card>
                <CardContent className="py-8 text-center">
                  <MessageCircle className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
                  <p className="text-muted-foreground">
                    Aucun commentaire pour le moment. Soyez le premier à donner votre avis !
                  </p>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default ProductDetails;
