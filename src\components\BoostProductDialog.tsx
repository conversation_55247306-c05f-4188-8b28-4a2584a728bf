
import { useState } from "react";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { Zap, Star, Crown, TrendingUp } from "lucide-react";

interface BoostProductDialogProps {
  productId: string;
  productName: string;
  children: React.ReactNode;
}

const BoostProductDialog = ({ productId, productName, children }: BoostProductDialogProps) => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);

  const boostPlans = [
    {
      id: 'basic',
      name: 'Boost Basic',
      price: 2900, // €29.00
      duration: 7,
      icon: Zap,
      features: [
        'Mis en vedette pendant 7 jours',
        'Badge "Boosted" visible',
        'Position prioritaire dans les résultats',
        'Statistiques de base'
      ],
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200'
    },
    {
      id: 'premium',
      name: 'Boost Premium',
      price: 4900, // €49.00
      duration: 14,
      icon: Star,
      features: [
        'Mis en vedette pendant 14 jours',
        'Badge "Premium" exclusif',
        'Position top dans tous les résultats',
        'Statistiques avancées',
        'Mention dans la newsletter'
      ],
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      borderColor: 'border-purple-200',
      popular: true
    },
    {
      id: 'enterprise',
      name: 'Boost Enterprise',
      price: 9900, // €99.00
      duration: 30,
      icon: Crown,
      features: [
        'Mis en vedette pendant 30 jours',
        'Badge "Enterprise" premium',
        'Position #1 garantie',
        'Statistiques complètes',
        'Mention newsletter + réseaux sociaux',
        'Article de blog dédié'
      ],
      color: 'text-gold-600',
      bgColor: 'bg-yellow-50',
      borderColor: 'border-yellow-200'
    }
  ];

  const handleBoost = async (plan: typeof boostPlans[0]) => {
    if (!user) {
      toast({
        variant: "destructive",
        title: "Connexion requise",
        description: "Vous devez être connecté pour booster un produit",
      });
      return;
    }

    setIsLoading(true);
    
    try {
      // Créer une session de paiement Stripe
      const { data, error } = await supabase.functions.invoke('create-boost-checkout', {
        body: {
          productId,
          productName,
          planId: plan.id,
          amount: plan.price,
          duration: plan.duration
        }
      });

      if (error) throw error;

      // Rediriger vers Stripe Checkout
      if (data?.url) {
        window.open(data.url, '_blank');
      }

    } catch (error) {
      console.error('Erreur lors du boost:', error);
      toast({
        variant: "destructive",
        title: "Erreur",
        description: "Impossible de créer la session de paiement. Veuillez réessayer.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <TrendingUp className="w-5 h-5 text-primary" />
            <span>Booster "{productName}"</span>
          </DialogTitle>
          <DialogDescription>
            Mettez votre produit en vedette pour augmenter sa visibilité et attirer plus d'attention
          </DialogDescription>
        </DialogHeader>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
          {boostPlans.map((plan) => {
            const IconComponent = plan.icon;
            return (
              <Card 
                key={plan.id} 
                className={`relative ${plan.popular ? 'ring-2 ring-primary' : ''} ${plan.borderColor}`}
              >
                {plan.popular && (
                  <Badge className="absolute -top-2 left-1/2 transform -translate-x-1/2 bg-primary">
                    Plus populaire
                  </Badge>
                )}
                <CardHeader className={`text-center ${plan.bgColor}`}>
                  <div className="flex justify-center mb-2">
                    <IconComponent className={`w-8 h-8 ${plan.color}`} />
                  </div>
                  <CardTitle className="text-lg">{plan.name}</CardTitle>
                  <div className="text-3xl font-bold">
                    €{(plan.price / 100).toFixed(0)}
                  </div>
                  <CardDescription>
                    {plan.duration} jours de mise en vedette
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <ul className="space-y-2 text-sm">
                    {plan.features.map((feature, index) => (
                      <li key={index} className="flex items-start space-x-2">
                        <span className="text-green-500 mt-1">✓</span>
                        <span>{feature}</span>
                      </li>
                    ))}
                  </ul>
                  <Button 
                    onClick={() => handleBoost(plan)}
                    disabled={isLoading}
                    className="w-full"
                    variant={plan.popular ? "default" : "outline"}
                  >
                    {isLoading ? "Chargement..." : `Choisir ${plan.name}`}
                  </Button>
                </CardContent>
              </Card>
            );
          })}
        </div>
        
        <div className="mt-6 p-4 bg-muted rounded-lg">
          <h4 className="font-semibold mb-2">Pourquoi booster votre produit ?</h4>
          <ul className="text-sm space-y-1 text-muted-foreground">
            <li>• Augmentez la visibilité de votre produit</li>
            <li>• Attirez plus de votes et de commentaires</li>
            <li>• Accédez à des statistiques détaillées</li>
            <li>• Bénéficiez d'une promotion sur nos réseaux sociaux</li>
          </ul>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default BoostProductDialog;
