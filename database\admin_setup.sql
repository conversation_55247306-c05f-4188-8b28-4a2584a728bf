-- AYOHUB Admin Setup SQL Script
-- Run this script in your Supabase SQL editor to set up admin functionality

-- 1. Ensure profiles table has admin_role column
ALTER TABLE profiles 
ADD COLUMN IF NOT EXISTS admin_role BOOLEAN DEFAULT FALSE;

-- 2. Create admin user function
CREATE OR REPLACE FUNCTION create_admin_user(
  user_email TEXT,
  user_password TEXT,
  user_name TEXT DEFAULT 'Admin User'
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  new_user_id UUID;
BEGIN
  -- Create user in auth.users
  INSERT INTO auth.users (
    id,
    instance_id,
    email,
    encrypted_password,
    email_confirmed_at,
    created_at,
    updated_at,
    aud,
    role
  )
  VALUES (
    gen_random_uuid(),
    '00000000-0000-0000-0000-000000000000',
    user_email,
    crypt(user_password, gen_salt('bf')),
    NOW(),
    NOW(),
    NOW(),
    'authenticated',
    'authenticated'
  )
  RETURNING id INTO new_user_id;

  -- <PERSON>reate profile with admin privileges
  INSERT INTO profiles (
    id,
    email,
    full_name,
    admin_role,
    role,
    bio,
    created_at,
    updated_at
  )
  VALUES (
    new_user_id,
    user_email,
    user_name,
    TRUE,
    'admin',
    'Platform Administrator',
    NOW(),
    NOW()
  );

  RETURN new_user_id;
END;
$$;

-- 3. Create default admin user (if not exists)
DO $$
DECLARE
  admin_exists BOOLEAN;
BEGIN
  SELECT EXISTS(
    SELECT 1 FROM profiles WHERE email = '<EMAIL>'
  ) INTO admin_exists;
  
  IF NOT admin_exists THEN
    PERFORM create_admin_user(
      '<EMAIL>',
      'AyohubAdmin2024!',
      'AYOHUB Administrator'
    );
    RAISE NOTICE 'Default admin user created: <EMAIL>';
  ELSE
    -- Update existing user to admin
    UPDATE profiles 
    SET 
      admin_role = TRUE,
      role = 'admin',
      bio = 'Platform Administrator'
    WHERE email = '<EMAIL>';
    RAISE NOTICE 'Existing user updated to admin: <EMAIL>';
  END IF;
END;
$$;

-- 4. Row Level Security Policies for Admin Access

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Admins can view all profiles" ON profiles;
DROP POLICY IF EXISTS "Admins can update all profiles" ON profiles;
DROP POLICY IF EXISTS "Admins can delete profiles" ON profiles;
DROP POLICY IF EXISTS "Admins can manage all products" ON products;
DROP POLICY IF EXISTS "Admins can manage boost requests" ON boost_requests;
DROP POLICY IF EXISTS "Admins can manage votes" ON votes;
DROP POLICY IF EXISTS "Admins can manage comments" ON product_comments;

-- Admin policies for profiles table
CREATE POLICY "Admins can view all profiles" ON profiles
FOR SELECT USING (
  auth.uid() IN (
    SELECT id FROM profiles WHERE admin_role = true
  )
);

CREATE POLICY "Admins can update all profiles" ON profiles
FOR UPDATE USING (
  auth.uid() IN (
    SELECT id FROM profiles WHERE admin_role = true
  )
);

CREATE POLICY "Admins can delete profiles" ON profiles
FOR DELETE USING (
  auth.uid() IN (
    SELECT id FROM profiles WHERE admin_role = true
  )
);

-- Admin policies for products table
CREATE POLICY "Admins can manage all products" ON products
FOR ALL USING (
  auth.uid() IN (
    SELECT id FROM profiles WHERE admin_role = true
  )
);

-- Admin policies for boost_requests table
CREATE POLICY "Admins can manage boost requests" ON boost_requests
FOR ALL USING (
  auth.uid() IN (
    SELECT id FROM profiles WHERE admin_role = true
  )
);

-- Admin policies for votes table
CREATE POLICY "Admins can manage votes" ON votes
FOR ALL USING (
  auth.uid() IN (
    SELECT id FROM profiles WHERE admin_role = true
  )
);

-- Admin policies for comments table
CREATE POLICY "Admins can manage comments" ON product_comments
FOR ALL USING (
  auth.uid() IN (
    SELECT id FROM profiles WHERE admin_role = true
  )
);

-- 5. Create admin helper functions

-- Function to check if current user is admin
CREATE OR REPLACE FUNCTION is_admin()
RETURNS BOOLEAN
LANGUAGE sql
SECURITY DEFINER
STABLE
AS $$
  SELECT EXISTS(
    SELECT 1 FROM profiles 
    WHERE id = auth.uid() AND admin_role = true
  );
$$;

-- Function to get admin users
CREATE OR REPLACE FUNCTION get_admin_users()
RETURNS TABLE(
  id UUID,
  email TEXT,
  full_name TEXT,
  created_at TIMESTAMPTZ
)
LANGUAGE sql
SECURITY DEFINER
AS $$
  SELECT p.id, p.email, p.full_name, p.created_at
  FROM profiles p
  WHERE p.admin_role = true
  ORDER BY p.created_at DESC;
$$;

-- Function to promote user to admin
CREATE OR REPLACE FUNCTION promote_to_admin(user_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Check if current user is admin
  IF NOT is_admin() THEN
    RAISE EXCEPTION 'Only admins can promote users';
  END IF;

  UPDATE profiles 
  SET 
    admin_role = true,
    role = 'admin',
    updated_at = NOW()
  WHERE id = user_id;

  RETURN FOUND;
END;
$$;

-- Function to revoke admin privileges
CREATE OR REPLACE FUNCTION revoke_admin(user_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Check if current user is admin
  IF NOT is_admin() THEN
    RAISE EXCEPTION 'Only admins can revoke admin privileges';
  END IF;

  -- Prevent revoking own admin privileges
  IF user_id = auth.uid() THEN
    RAISE EXCEPTION 'Cannot revoke your own admin privileges';
  END IF;

  UPDATE profiles 
  SET 
    admin_role = false,
    role = 'supporter',
    updated_at = NOW()
  WHERE id = user_id;

  RETURN FOUND;
END;
$$;

-- 6. Create admin statistics view
CREATE OR REPLACE VIEW admin_stats AS
SELECT 
  (SELECT COUNT(*) FROM profiles) as total_users,
  (SELECT COUNT(*) FROM profiles WHERE admin_role = true) as admin_users,
  (SELECT COUNT(*) FROM products) as total_products,
  (SELECT COUNT(*) FROM products WHERE created_at >= CURRENT_DATE - INTERVAL '7 days') as products_this_week,
  (SELECT COUNT(*) FROM votes) as total_votes,
  (SELECT COUNT(*) FROM votes WHERE created_at >= CURRENT_DATE - INTERVAL '7 days') as votes_this_week,
  (SELECT COUNT(*) FROM boost_requests) as total_boost_requests,
  (SELECT COUNT(*) FROM boost_requests WHERE status = 'pending') as pending_boost_requests;

-- Grant access to admin stats view
GRANT SELECT ON admin_stats TO authenticated;

-- 7. Enable RLS on all tables (if not already enabled)
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE votes ENABLE ROW LEVEL SECURITY;
ALTER TABLE product_comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE boost_requests ENABLE ROW LEVEL SECURITY;

-- 8. Create audit log table for admin actions
CREATE TABLE IF NOT EXISTS admin_audit_log (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  admin_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  action TEXT NOT NULL,
  target_type TEXT NOT NULL, -- 'user', 'product', 'comment', etc.
  target_id UUID,
  details JSONB,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enable RLS on audit log
ALTER TABLE admin_audit_log ENABLE ROW LEVEL SECURITY;

-- Only admins can view audit logs
CREATE POLICY "Admins can view audit logs" ON admin_audit_log
FOR SELECT USING (
  auth.uid() IN (
    SELECT id FROM profiles WHERE admin_role = true
  )
);

-- Function to log admin actions
CREATE OR REPLACE FUNCTION log_admin_action(
  action_type TEXT,
  target_type TEXT,
  target_id UUID DEFAULT NULL,
  action_details JSONB DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  log_id UUID;
BEGIN
  INSERT INTO admin_audit_log (
    admin_id,
    action,
    target_type,
    target_id,
    details
  )
  VALUES (
    auth.uid(),
    action_type,
    target_type,
    target_id,
    action_details
  )
  RETURNING id INTO log_id;

  RETURN log_id;
END;
$$;

-- 9. Verification queries
-- Run these to verify setup

-- Check admin users
SELECT 'Admin Users:' as info;
SELECT email, full_name, admin_role, created_at 
FROM profiles 
WHERE admin_role = true;

-- Check admin stats
SELECT 'Platform Statistics:' as info;
SELECT * FROM admin_stats;

-- Test admin function
SELECT 'Admin Check:' as info;
SELECT is_admin() as current_user_is_admin;

RAISE NOTICE 'Admin setup completed successfully!';
RAISE NOTICE 'Default admin credentials: <EMAIL> / AyohubAdmin2024!';
RAISE NOTICE 'Access admin dashboard at: /admin';
