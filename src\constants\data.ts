// African countries with their flags
export const AFRICAN_COUNTRIES = [
  { code: 'DZ', name: 'Algeria', flag: '🇩🇿' },
  { code: 'A<PERSON>', name: 'Angola', flag: '🇦🇴' },
  { code: 'B<PERSON>', name: 'Benin', flag: '🇧🇯' },
  { code: 'B<PERSON>', name: 'Botswana', flag: '🇧🇼' },
  { code: 'BF', name: 'Burkina Faso', flag: '🇧🇫' },
  { code: 'BI', name: 'Burundi', flag: '🇧🇮' },
  { code: 'CV', name: 'Cape Verde', flag: '🇨🇻' },
  { code: 'CM', name: 'Cameroon', flag: '🇨🇲' },
  { code: 'CF', name: 'Central African Republic', flag: '🇨🇫' },
  { code: 'TD', name: 'Chad', flag: '🇹🇩' },
  { code: 'KM', name: 'Comoros', flag: '🇰🇲' },
  { code: 'CG', name: 'Congo', flag: '🇨🇬' },
  { code: 'CD', name: 'Democratic Republic of the Congo', flag: '🇨🇩' },
  { code: 'CI', name: 'Côte d\'Ivoire', flag: '🇨🇮' },
  { code: 'DJ', name: 'Djibouti', flag: '🇩🇯' },
  { code: 'EG', name: 'Egypt', flag: '🇪🇬' },
  { code: 'GQ', name: 'Equatorial Guinea', flag: '🇬🇶' },
  { code: 'ER', name: 'Eritrea', flag: '🇪🇷' },
  { code: 'SZ', name: 'Eswatini', flag: '🇸🇿' },
  { code: 'ET', name: 'Ethiopia', flag: '🇪🇹' },
  { code: 'GA', name: 'Gabon', flag: '🇬🇦' },
  { code: 'GM', name: 'Gambia', flag: '🇬🇲' },
  { code: 'GH', name: 'Ghana', flag: '🇬🇭' },
  { code: 'GN', name: 'Guinea', flag: '🇬🇳' },
  { code: 'GW', name: 'Guinea-Bissau', flag: '🇬🇼' },
  { code: 'KE', name: 'Kenya', flag: '🇰🇪' },
  { code: 'LS', name: 'Lesotho', flag: '🇱🇸' },
  { code: 'LR', name: 'Liberia', flag: '🇱🇷' },
  { code: 'LY', name: 'Libya', flag: '🇱🇾' },
  { code: 'MG', name: 'Madagascar', flag: '🇲🇬' },
  { code: 'MW', name: 'Malawi', flag: '🇲🇼' },
  { code: 'ML', name: 'Mali', flag: '🇲🇱' },
  { code: 'MR', name: 'Mauritania', flag: '🇲🇷' },
  { code: 'MU', name: 'Mauritius', flag: '🇲🇺' },
  { code: 'MA', name: 'Morocco', flag: '🇲🇦' },
  { code: 'MZ', name: 'Mozambique', flag: '🇲🇿' },
  { code: 'NA', name: 'Namibia', flag: '🇳🇦' },
  { code: 'NE', name: 'Niger', flag: '🇳🇪' },
  { code: 'NG', name: 'Nigeria', flag: '🇳🇬' },
  { code: 'RW', name: 'Rwanda', flag: '🇷🇼' },
  { code: 'ST', name: 'São Tomé and Príncipe', flag: '🇸🇹' },
  { code: 'SN', name: 'Senegal', flag: '🇸🇳' },
  { code: 'SC', name: 'Seychelles', flag: '🇸🇨' },
  { code: 'SL', name: 'Sierra Leone', flag: '🇸🇱' },
  { code: 'SO', name: 'Somalia', flag: '🇸🇴' },
  { code: 'ZA', name: 'South Africa', flag: '🇿🇦' },
  { code: 'SS', name: 'South Sudan', flag: '🇸🇸' },
  { code: 'SD', name: 'Sudan', flag: '🇸🇩' },
  { code: 'TZ', name: 'Tanzania', flag: '🇹🇿' },
  { code: 'TG', name: 'Togo', flag: '🇹🇬' },
  { code: 'TN', name: 'Tunisia', flag: '🇹🇳' },
  { code: 'UG', name: 'Uganda', flag: '🇺🇬' },
  { code: 'ZM', name: 'Zambia', flag: '🇿🇲' },
  { code: 'ZW', name: 'Zimbabwe', flag: '🇿🇼' }
];

// Tech categories relevant to African innovation
export const TECH_CATEGORIES = [
  { id: 'fintech', name: 'FinTech', icon: '💰', description: 'Financial Technology' },
  { id: 'edtech', name: 'EdTech', icon: '📚', description: 'Education Technology' },
  { id: 'healthtech', name: 'HealthTech', icon: '🏥', description: 'Healthcare Technology' },
  { id: 'agritech', name: 'AgriTech', icon: '🌾', description: 'Agriculture Technology' },
  { id: 'cleantech', name: 'CleanTech', icon: '🌱', description: 'Clean Technology' },
  { id: 'ecommerce', name: 'E-commerce', icon: '🛒', description: 'Electronic Commerce' },
  { id: 'logistics', name: 'Logistics', icon: '🚚', description: 'Supply Chain & Logistics' },
  { id: 'entertainment', name: 'Entertainment', icon: '🎬', description: 'Media & Entertainment' },
  { id: 'mobility', name: 'Mobility', icon: '🚗', description: 'Transportation & Mobility' },
  { id: 'proptech', name: 'PropTech', icon: '🏠', description: 'Property Technology' },
  { id: 'insurtech', name: 'InsurTech', icon: '🛡️', description: 'Insurance Technology' },
  { id: 'govtech', name: 'GovTech', icon: '🏛️', description: 'Government Technology' },
  { id: 'cybersecurity', name: 'Cybersecurity', icon: '🔒', description: 'Security Technology' },
  { id: 'ai', name: 'AI/ML', icon: '🤖', description: 'Artificial Intelligence' },
  { id: 'blockchain', name: 'Blockchain', icon: '⛓️', description: 'Blockchain Technology' },
  { id: 'iot', name: 'IoT', icon: '📡', description: 'Internet of Things' },
  { id: 'saas', name: 'SaaS', icon: '☁️', description: 'Software as a Service' },
  { id: 'gaming', name: 'Gaming', icon: '🎮', description: 'Gaming & Interactive' },
  { id: 'social', name: 'Social', icon: '👥', description: 'Social Networks' },
  { id: 'other', name: 'Other', icon: '🔧', description: 'Other Technologies' }
];

// Product status options
export const PRODUCT_STATUS = [
  { id: 'idea', name: 'Idea', color: 'bg-gray-500/20 text-gray-700 border-gray-500/30' },
  { id: 'mvp', name: 'MVP', color: 'bg-accent/20 text-accent border-accent/30' },
  { id: 'beta', name: 'Beta', color: 'bg-primary/20 text-primary border-primary/30' },
  { id: 'launch', name: 'Launch', color: 'bg-green-500/20 text-green-700 border-green-500/30' },
  { id: 'growth', name: 'Growth', color: 'bg-blue-500/20 text-blue-700 border-blue-500/30' },
  { id: 'mature', name: 'Mature', color: 'bg-purple-500/20 text-purple-700 border-purple-500/30' }
];

// User roles
export const USER_ROLES = [
  { id: 'maker', name: 'Maker', description: 'Product creator/entrepreneur' },
  { id: 'investor', name: 'Investor', description: 'Investor or VC' },
  { id: 'supporter', name: 'Supporter', description: 'Community supporter' }
];
