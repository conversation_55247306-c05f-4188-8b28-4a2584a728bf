-- Admin Setup Script for Africa Tech Spotlight
-- Run this in Supabase SQL Editor after registering your admin account

-- Step 1: Check existing users
SELECT 
  id,
  email,
  created_at,
  email_confirmed_at
FROM auth.users
ORDER BY created_at DESC;

-- Step 2: Check existing profiles
SELECT 
  id,
  email,
  full_name,
  admin_role,
  created_at
FROM profiles
ORDE<PERSON> BY created_at DESC;

-- Step 3: Grant admin role to a specific user
-- REPLACE '<EMAIL>' with your actual email address
UPDATE profiles 
SET admin_role = true 
WHERE email = '<EMAIL>';

-- Step 4: Verify admin role was granted
SELECT 
  email,
  full_name,
  admin_role,
  'Admin role granted successfully' as status
FROM profiles 
WHERE admin_role = true;

-- Step 5: Create additional admin users if needed
-- Uncomment and modify as needed:
-- UPDATE profiles SET admin_role = true WHERE email = '<EMAIL>';
-- UPDATE profiles SET admin_role = true WHERE email = '<EMAIL>';

-- Step 6: View all admin users
SELECT 
  email,
  full_name,
  created_at,
  'Admin User' as role
FROM profiles 
WHERE admin_role = true
ORDE<PERSON> BY created_at;

-- Optional: Remove admin role from a user
-- UPDATE profiles SET admin_role = false WHERE email = '<EMAIL>';
