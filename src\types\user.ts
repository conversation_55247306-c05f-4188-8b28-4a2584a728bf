export interface User {
  id: string;
  name: string;
  email: string;
  avatar: string;
  bio: string;
  role: 'Maker' | 'Investisseur' | 'Supporter';
  country: string;
  joinedAt: string;
  productsLaunched: string[];
  productsUpvoted: string[];
  followers: number;
  following: number;
}

export interface Comment {
  id: string;
  productId: string;
  userId: string;
  userName: string;
  userAvatar: string;
  content: string;
  createdAt: string;
  likes: number;
  hasLiked?: boolean;
}