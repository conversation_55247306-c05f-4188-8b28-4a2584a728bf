
import React, { createContext, useContext, useState, useEffect } from 'react';

interface LanguageContextType {
  language: string;
  setLanguage: (lang: string) => void;
  t: (key: string) => string;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

const translations = {
  fr: {
    // Navigation
    'nav.home': 'Accueil',
    'nav.products': 'Produits',
    'nav.about': 'À propos',
    'nav.login': 'Connexion',
    'nav.dashboard': 'Dashboard',
    'nav.profile': 'Profil',
    'nav.logout': 'Déconnexion',
    
    // Hero Section
    'hero.title': 'Découvrez les innovations tech africaines',
    'hero.subtitle': 'La plateforme qui met en lumière les startups et produits technologiques africains les plus prometteurs',
    'hero.cta': 'Découvrir les produits',
    'hero.submit': 'Soumettre un produit',
    
    // Products
    'products.title': 'Produits du jour',
    'products.subtitle': 'Découvrez les innovations les plus votées par la communauté tech africaine',
    'products.search': 'Rechercher un produit, pays ou description...',
    'products.category': 'Catégorie',
    'products.empty.title': 'Aucun produit n\'a encore été soumis.',
    'products.empty.subtitle': 'Soyez le premier à partager votre innovation !',
    'products.no_results': 'Aucun produit ne correspond à vos critères de recherche.',
    'products.search': 'Rechercher un produit, pays ou description...',
    'products.category': 'Catégorie',
    'products.all': 'Toutes les catégories',
    'products.sort': 'Trier par',
    'products.votes': 'Plus votés',
    'products.recent': 'Plus récents',
    'products.visit': 'Visiter le site',
    'products.boost': 'Booster ce produit',
    'products.vote': 'Voter',
    'products.voted': 'Voté',
    
    // Auth
    'auth.title': 'Rejoignez AYOHUB',
    'auth.subtitle': 'Connectez-vous ou créez un compte pour découvrir et promouvoir l\'innovation africaine',
    'auth.signin': 'Connexion',
    'auth.signup': 'Inscription',
    'auth.email': 'Email',
    'auth.password': 'Mot de passe',
    'auth.fullname': 'Nom complet',
    'auth.signin.button': 'Se connecter',
    'auth.signup.button': 'S\'inscrire',
    
    // About
    'about.title': 'À propos d\'AYOHUB',
    'about.mission.title': 'Notre Mission',
    'about.mission.content': 'AYOHUB est la première plateforme dédiée à la promotion et à la découverte des innovations technologiques africaines. Nous croyons que l\'Afrique regorge de talents et d\'idées révolutionnaires qui méritent d\'être mises en lumière.',
    'about.vision.title': 'Notre Vision',
    'about.vision.content': 'Créer un écosystème dynamique où les entrepreneurs africains peuvent présenter leurs innovations, recevoir des feedbacks constructifs et établir des connexions précieuses avec des investisseurs et des partenaires potentiels.',
    'about.values.title': 'Nos Valeurs',
    'about.values.innovation': 'Innovation',
    'about.values.innovation.desc': 'Nous célébrons la créativité et l\'innovation sous toutes ses formes.',
    'about.values.community': 'Communauté',
    'about.values.community.desc': 'Nous construisons une communauté solidaire et collaborative.',
    'about.values.excellence': 'Excellence',
    'about.values.excellence.desc': 'Nous visons l\'excellence dans tout ce que nous faisons.',
    'about.team.title': 'Notre Équipe',
    'about.contact.title': 'Contactez-nous',
    'about.contact.content': 'Vous avez des questions ou souhaitez collaborer avec nous ? N\'hésitez pas à nous contacter à <EMAIL>',

    // Common UI
    'common.loading': 'Chargement...',
    'common.error': 'Erreur',
    'common.success': 'Succès',
    'common.cancel': 'Annuler',
    'common.save': 'Enregistrer',
    'common.delete': 'Supprimer',
    'common.edit': 'Modifier',
    'common.back': 'Retour',
    'common.submit': 'Soumettre',
    'common.search': 'Rechercher',
    'common.filter': 'Filtrer',
    'common.reset': 'Réinitialiser',

    // Dashboard
    'dashboard.title': 'Dashboard',
    'dashboard.welcome': 'Bienvenue, {name} !',
    'dashboard.admin': 'Dashboard Admin',
    'dashboard.admin.subtitle': 'Gérez les utilisateurs, produits et demandes de boost',

    // Product submission
    'submit.title': 'Soumettre un produit',
    'submit.name': 'Nom du produit',
    'submit.namePlaceholder': 'Entrez le nom de votre produit',
    'submit.description': 'Description',
    'submit.descriptionPlaceholder': 'Décrivez votre produit et ses fonctionnalités',
    'submit.website': 'Site web',
    'submit.websitePlaceholder': 'https://monproduit.com',
    'submit.logo': 'URL du logo',
    'submit.logoPlaceholder': 'https://monproduit.com/logo.png',
    'submit.category': 'Catégorie',
    'submit.categoryPlaceholder': 'Sélectionnez une catégorie',
    'submit.status': 'Statut',
    'submit.statusPlaceholder': 'Sélectionnez le statut de votre produit',
    'submit.country': 'Pays',
    'submit.countryPlaceholder': 'Dans quel pays est basé votre produit ?',
    'submit.tags': 'Tags',
    'submit.tagsPlaceholder': 'Ajoutez un tag',
    'submit.addTag': 'Ajouter',
    'submit.button': 'Soumettre le produit',
    'submit.submitting': 'Soumission en cours...',
    'submit.success': 'Produit soumis avec succès !',
    'submit.error': 'Erreur lors de la soumission',
    'submit.login_required': 'Connexion requise pour soumettre un produit',
    'submit.form.title': 'Soumettre votre produit',
    'submit.form.subtitle': 'Partagez votre innovation avec la communauté tech africaine',

    // Widget section
    'widget.title': 'Widget "Featured on AYOHUB"',
    'widget.subtitle': 'Créez un widget pour montrer que vous êtes sur AYOHUB',
    'widget.description': 'Ajoutez ce widget à votre site pour montrer votre présence sur AYOHUB',
    'widget.benefits.title': 'Avantages du widget',
    'widget.benefits.credibility': 'Augmente votre crédibilité',
    'widget.benefits.traffic': 'Génère du trafic vers AYOHUB',
    'widget.benefits.customizable': 'Entièrement personnalisable',
    'widget.benefits.responsive': 'Responsive et accessible',
    'widget.benefits.optimized': 'Code optimisé et léger',
    'widget.generate': 'Générer un widget "Featured on AYOHUB"',
    'widget.generator.title': 'Générateur de Widget AYOHUB',
    'widget.generator.description': 'Créez un widget personnalisé pour montrer que votre produit est présenté sur AYOHUB',
    'widget.config.title': 'Configuration',
    'widget.config.product_name': 'Nom du produit',
    'widget.config.product_name_placeholder': 'Mon Super Produit',
    'widget.config.product_url': 'URL du produit',
    'widget.config.product_url_placeholder': 'https://monproduit.com',
    'widget.config.custom_text': 'Texte personnalisé (optionnel)',
    'widget.config.custom_text_placeholder': 'Texte personnalisé pour remplacer le texte par défaut',
    'widget.config.theme': 'Thème',
    'widget.config.size': 'Taille',
    'widget.config.show_logo': 'Afficher le logo AYOHUB',
    'widget.config.show_votes': 'Afficher le badge de votes',
    'widget.preview': 'Aperçu',
    'widget.code': 'Code HTML',
    'widget.copy': 'Copier',
    'widget.download': 'Télécharger',
    'widget.instructions.title': 'Instructions d\'utilisation',
    'widget.instructions.step1': 'Copiez le code HTML ci-dessus',
    'widget.instructions.step2': 'Collez-le dans votre site web',
    'widget.instructions.step3': 'Le widget sera cliquable et redirigera vers votre produit',
    'widget.instructions.step4': 'Personnalisez les couleurs selon votre marque',
    'widget.featured_text': 'Présenté sur AYOHUB',
    'widget.votes': 'votes',
    'widget.copy_success': 'Code copié !',
    'widget.copy_description': 'Le code du widget a été copié dans votre presse-papiers',

    // Comments
    'comments.title': 'Commentaires',
    'comments.add': 'Ajouter un commentaire',
    'comments.placeholder': 'Partagez votre avis sur ce produit...',
    'comments.publish': 'Publier le commentaire',
    'comments.login_required': 'Connectez-vous pour laisser un commentaire',
    'comments.empty': 'Aucun commentaire pour le moment. Soyez le premier à donner votre avis !',
    'comments.success': 'Commentaire ajouté avec succès',
    'comments.error': 'Impossible d\'ajouter le commentaire',

    // User Profile
    'profile.edit': 'Modifier le profil',
    'profile.follow': 'Suivre',
    'profile.unfollow': 'Ne plus suivre',
    'profile.message': 'Message',
    'profile.followers': 'abonnés',
    'profile.following': 'abonnements',
    'profile.products_launched': 'produits lancés',
    'profile.votes_given': 'Votes donnés',
    'profile.community_impact': 'Impact communauté',
    'profile.recent_activity': 'Activité récente',
    'profile.member_since': 'Membre depuis',
    'profile.no_activity': 'Aucune activité récente',
    'profile.voted_for': 'A voté pour',
    'profile.commented_on': 'A commenté sur',
    'profile.launched_product': 'A lancé un nouveau produit',
    'profile.joined_community': 'A rejoint la communauté',

    // Product Details
    'product.back': 'Retour',
    'product.user': 'Utilisateur',
    'product.created_by': 'Créé par',
    'product.created_on': 'Créé le',
    'product.website': 'Site web',
    'product.vote': 'Voter',
    'product.voted': 'Voté',
    'product.boost': 'Booster',
    'product.share': 'Partager',
    'product.report': 'Signaler',
    'product.loading': 'Chargement du produit...',
    'product.not_found': 'Produit non trouvé',
    'product.error': 'Erreur lors du chargement du produit',

    // Auth
    'auth.title': 'Rejoignez AYOHUB',
    'auth.subtitle': 'Connectez-vous ou créez un compte pour découvrir et promouvoir l\'innovation africaine',
    'auth.signin_tab': 'Connexion',
    'auth.signup_tab': 'Inscription',
    'auth.email': 'Email',
    'auth.password': 'Mot de passe',
    'auth.fullname': 'Nom complet',
    'auth.signin_button': 'Se connecter',
    'auth.signup_button': 'S\'inscrire',
    'auth.signin_loading': 'Connexion...',
    'auth.signup_loading': 'Inscription...',
    'auth.loading': 'Chargement...',

    // Footer
    'footer.description': 'La plateforme qui met en lumière l\'innovation technologique africaine. Découvrez, votez et suivez les startups les plus prometteuses du continent.',
    'footer.newsletter.title': 'Newsletter',
    'footer.newsletter.placeholder': 'Votre email',
    'footer.navigation.title': 'Navigation',
    'footer.navigation.blog': 'Blog',
    'footer.community.title': 'Communauté',
    'footer.community.discord': 'Discord',
    'footer.community.slack': 'Slack',
    'footer.community.events': 'Événements',
    'footer.community.partners': 'Partenaires',
    'footer.legal.privacy': 'Politique de confidentialité',
    'footer.legal.terms': 'Conditions d\'utilisation',
    'footer.copyright': '© 2024 AYOHUB. Tous droits réservés.',
  },
  en: {
    // Navigation
    'nav.home': 'Home',
    'nav.products': 'Products',
    'nav.about': 'About',
    'nav.login': 'Login',
    'nav.dashboard': 'Dashboard',
    'nav.profile': 'Profile',
    'nav.logout': 'Logout',
    
    // Hero Section
    'hero.title': 'Discover African Tech Innovations',
    'hero.subtitle': 'The platform that highlights the most promising African tech startups and products',
    'hero.cta': 'Discover Products',
    'hero.submit': 'Submit a Product',
    
    // Products
    'products.title': 'Products of the Day',
    'products.subtitle': 'Discover the most voted innovations by the African tech community',
    'products.search': 'Search for a product, country or description...',
    'products.category': 'Category',
    'products.all': 'All categories',
    'products.sort': 'Sort by',
    'products.votes': 'Most voted',
    'products.recent': 'Most recent',
    'products.visit': 'Visit website',
    'products.boost': 'Boost this product',
    'products.vote': 'Vote',
    'products.voted': 'Voted',
    
    // Auth
    'auth.title': 'Join AYOHUB',
    'auth.subtitle': 'Sign in or create an account to discover and promote African innovation',
    'auth.signin': 'Sign In',
    'auth.signup': 'Sign Up',
    'auth.email': 'Email',
    'auth.password': 'Password',
    'auth.fullname': 'Full Name',
    'auth.signin.button': 'Sign In',
    'auth.signup.button': 'Sign Up',
    
    // About
    'about.title': 'About AYOHUB',
    'about.mission.title': 'Our Mission',
    'about.mission.content': 'AYOHUB is the first platform dedicated to promoting and discovering African technological innovations. We believe that Africa is full of talent and revolutionary ideas that deserve to be highlighted.',
    'about.vision.title': 'Our Vision',
    'about.vision.content': 'To create a dynamic ecosystem where African entrepreneurs can showcase their innovations, receive constructive feedback and establish valuable connections with potential investors and partners.',
    'about.values.title': 'Our Values',
    'about.values.innovation': 'Innovation',
    'about.values.innovation.desc': 'We celebrate creativity and innovation in all its forms.',
    'about.values.community': 'Community',
    'about.values.community.desc': 'We build a supportive and collaborative community.',
    'about.values.excellence': 'Excellence',
    'about.values.excellence.desc': 'We strive for excellence in everything we do.',
    'about.team.title': 'Our Team',
    'about.contact.title': 'Contact Us',
    'about.contact.content': 'Have questions or want to collaborate with us? Feel free to contact <NAME_EMAIL>',

    // Common UI
    'common.loading': 'Loading...',
    'common.error': 'Error',
    'common.success': 'Success',
    'common.cancel': 'Cancel',
    'common.save': 'Save',
    'common.delete': 'Delete',
    'common.edit': 'Edit',
    'common.back': 'Back',
    'common.submit': 'Submit',
    'common.search': 'Search',
    'common.filter': 'Filter',
    'common.reset': 'Reset',

    // Dashboard
    'dashboard.title': 'Dashboard',
    'dashboard.welcome': 'Welcome, {name}!',
    'dashboard.admin': 'Admin Dashboard',
    'dashboard.admin.subtitle': 'Manage users, products and boost requests',

    // Product submission
    'submit.title': 'Submit a Product',
    'submit.name': 'Product Name',
    'submit.namePlaceholder': 'Enter your product name',
    'submit.description': 'Description',
    'submit.descriptionPlaceholder': 'Describe your product and its features',
    'submit.website': 'Website',
    'submit.websitePlaceholder': 'https://myproduct.com',
    'submit.logo': 'Logo URL',
    'submit.logoPlaceholder': 'https://myproduct.com/logo.png',
    'submit.category': 'Category',
    'submit.categoryPlaceholder': 'Select a category',
    'submit.status': 'Status',
    'submit.statusPlaceholder': 'Select your product status',
    'submit.country': 'Country',
    'submit.countryPlaceholder': 'Which country is your product based in?',
    'submit.tags': 'Tags',
    'submit.tagsPlaceholder': 'Add a tag',
    'submit.addTag': 'Add',
    'submit.button': 'Submit Product',
    'submit.submitting': 'Submitting...',
    'submit.success': 'Product submitted successfully!',
    'submit.error': 'Error submitting product',
    'submit.login_required': 'Login required to submit a product',
    'submit.form.title': 'Submit your product',
    'submit.form.subtitle': 'Share your innovation with the African tech community',

    // Widget section
    'widget.title': 'Widget "Featured on AYOHUB"',
    'widget.subtitle': 'Create a widget to show you\'re on AYOHUB',
    'widget.description': 'Add this widget to your site to show your presence on AYOHUB',
    'widget.benefits.title': 'Widget benefits',
    'widget.benefits.credibility': 'Increases your credibility',
    'widget.benefits.traffic': 'Generates traffic to AYOHUB',
    'widget.benefits.customizable': 'Fully customizable',
    'widget.benefits.responsive': 'Responsive and accessible',
    'widget.benefits.optimized': 'Optimized and lightweight code',
    'widget.generate': 'Generate "Featured on AYOHUB" widget',
    'widget.generator.title': 'AYOHUB Widget Generator',
    'widget.generator.description': 'Create a custom widget to show that your product is featured on AYOHUB',
    'widget.config.title': 'Configuration',
    'widget.config.product_name': 'Product name',
    'widget.config.product_name_placeholder': 'My Awesome Product',
    'widget.config.product_url': 'Product URL',
    'widget.config.product_url_placeholder': 'https://myproduct.com',
    'widget.config.custom_text': 'Custom text (optional)',
    'widget.config.custom_text_placeholder': 'Custom text to replace the default text',
    'widget.config.theme': 'Theme',
    'widget.config.size': 'Size',
    'widget.config.show_logo': 'Show AYOHUB logo',
    'widget.config.show_votes': 'Show vote badge',
    'widget.preview': 'Preview',
    'widget.code': 'HTML Code',
    'widget.copy': 'Copy',
    'widget.download': 'Download',
    'widget.instructions.title': 'Usage instructions',
    'widget.instructions.step1': 'Copy the HTML code above',
    'widget.instructions.step2': 'Paste it into your website',
    'widget.instructions.step3': 'The widget will be clickable and redirect to your product',
    'widget.instructions.step4': 'Customize colors according to your brand',
    'widget.featured_text': 'Featured on AYOHUB',
    'widget.votes': 'votes',
    'widget.copy_success': 'Code copied!',
    'widget.copy_description': 'The widget code has been copied to your clipboard',

    // Comments
    'comments.title': 'Comments',
    'comments.add': 'Add a comment',
    'comments.placeholder': 'Share your thoughts about this product...',
    'comments.publish': 'Publish comment',
    'comments.login_required': 'Sign in to leave a comment',
    'comments.empty': 'No comments yet. Be the first to share your thoughts!',
    'comments.success': 'Comment added successfully',
    'comments.error': 'Unable to add comment',

    // User Profile
    'profile.edit': 'Edit Profile',
    'profile.follow': 'Follow',
    'profile.unfollow': 'Unfollow',
    'profile.message': 'Message',
    'profile.followers': 'followers',
    'profile.following': 'following',
    'profile.products_launched': 'products launched',
    'profile.votes_given': 'Votes given',
    'profile.community_impact': 'Community impact',
    'profile.recent_activity': 'Recent activity',
    'profile.member_since': 'Member since',
    'profile.no_activity': 'No recent activity',
    'profile.voted_for': 'Voted for',
    'profile.commented_on': 'Commented on',
    'profile.launched_product': 'Launched a new product',
    'profile.joined_community': 'Joined the community',

    // Product Details
    'product.back': 'Back',
    'product.user': 'User',
    'product.created_by': 'Created by',
    'product.created_on': 'Created on',
    'product.website': 'Website',
    'product.vote': 'Vote',
    'product.voted': 'Voted',
    'product.boost': 'Boost',
    'product.share': 'Share',
    'product.report': 'Report',
    'product.loading': 'Loading product...',
    'product.not_found': 'Product not found',
    'product.error': 'Error loading product',

    // Auth
    'auth.title': 'Join AYOHUB',
    'auth.subtitle': 'Sign in or create an account to discover and promote African innovation',
    'auth.signin_tab': 'Sign In',
    'auth.signup_tab': 'Sign Up',
    'auth.email': 'Email',
    'auth.password': 'Password',
    'auth.fullname': 'Full Name',
    'auth.signin_button': 'Sign In',
    'auth.signup_button': 'Sign Up',
    'auth.signin_loading': 'Signing in...',
    'auth.signup_loading': 'Signing up...',
    'auth.loading': 'Loading...',

    // Footer
    'footer.description': 'The platform that highlights African technological innovation. Discover, vote and follow the most promising startups on the continent.',
    'footer.newsletter.title': 'Newsletter',
    'footer.newsletter.placeholder': 'Your email',
    'footer.navigation.title': 'Navigation',
    'footer.navigation.blog': 'Blog',
    'footer.community.title': 'Community',
    'footer.community.discord': 'Discord',
    'footer.community.slack': 'Slack',
    'footer.community.events': 'Events',
    'footer.community.partners': 'Partners',
    'footer.legal.privacy': 'Privacy Policy',
    'footer.legal.terms': 'Terms of Service',
    'footer.copyright': '© 2024 AYOHUB. All rights reserved.',
  }
};

export const LanguageProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [language, setLanguage] = useState(() => {
    const saved = localStorage.getItem('ayohub_language');
    return saved || 'fr';
  });

  useEffect(() => {
    localStorage.setItem('ayohub_language', language);
  }, [language]);

  const t = (key: string): string => {
    return translations[language as keyof typeof translations]?.[key as keyof typeof translations.fr] || key;
  };

  const value = {
    language,
    setLanguage,
    t
  };

  return <LanguageContext.Provider value={value}>{children}</LanguageContext.Provider>;
};
