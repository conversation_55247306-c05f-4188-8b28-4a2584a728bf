
import React, { createContext, useContext, useState, useEffect } from 'react';

interface LanguageContextType {
  language: string;
  setLanguage: (lang: string) => void;
  t: (key: string) => string;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

const translations = {
  fr: {
    // Navigation
    'nav.home': 'Accueil',
    'nav.products': 'Produits',
    'nav.about': 'À propos',
    'nav.login': 'Connexion',
    'nav.dashboard': 'Dashboard',
    'nav.profile': 'Profil',
    'nav.logout': 'Déconnexion',
    
    // Hero Section
    'hero.title': 'Découvrez les innovations tech africaines',
    'hero.subtitle': 'La plateforme qui met en lumière les startups et produits technologiques africains les plus prometteurs',
    'hero.cta': 'Découvrir les produits',
    'hero.submit': 'Soumettre un produit',
    
    // Products
    'products.title': 'Produits du jour',
    'products.subtitle': 'Découvrez les innovations les plus votées par la communauté tech africaine',
    'products.search': 'Rechercher un produit, pays ou description...',
    'products.category': 'Catégorie',
    'products.all': 'Toutes les catégories',
    'products.sort': 'Trier par',
    'products.votes': 'Plus votés',
    'products.recent': 'Plus récents',
    'products.visit': 'Visiter le site',
    'products.boost': 'Booster ce produit',
    'products.vote': 'Voter',
    'products.voted': 'Voté',
    
    // Auth
    'auth.title': 'Rejoignez AYOHUB',
    'auth.subtitle': 'Connectez-vous ou créez un compte pour découvrir et promouvoir l\'innovation africaine',
    'auth.signin': 'Connexion',
    'auth.signup': 'Inscription',
    'auth.email': 'Email',
    'auth.password': 'Mot de passe',
    'auth.fullname': 'Nom complet',
    'auth.signin.button': 'Se connecter',
    'auth.signup.button': 'S\'inscrire',
    
    // About
    'about.title': 'À propos d\'AYOHUB',
    'about.mission.title': 'Notre Mission',
    'about.mission.content': 'AYOHUB est la première plateforme dédiée à la promotion et à la découverte des innovations technologiques africaines. Nous croyons que l\'Afrique regorge de talents et d\'idées révolutionnaires qui méritent d\'être mises en lumière.',
    'about.vision.title': 'Notre Vision',
    'about.vision.content': 'Créer un écosystème dynamique où les entrepreneurs africains peuvent présenter leurs innovations, recevoir des feedbacks constructifs et établir des connexions précieuses avec des investisseurs et des partenaires potentiels.',
    'about.values.title': 'Nos Valeurs',
    'about.values.innovation': 'Innovation',
    'about.values.innovation.desc': 'Nous célébrons la créativité et l\'innovation sous toutes ses formes.',
    'about.values.community': 'Communauté',
    'about.values.community.desc': 'Nous construisons une communauté solidaire et collaborative.',
    'about.values.excellence': 'Excellence',
    'about.values.excellence.desc': 'Nous visons l\'excellence dans tout ce que nous faisons.',
    'about.team.title': 'Notre Équipe',
    'about.contact.title': 'Contactez-nous',
    'about.contact.content': 'Vous avez des questions ou souhaitez collaborer avec nous ? N\'hésitez pas à nous contacter à <EMAIL>',

    // Common UI
    'common.loading': 'Chargement...',
    'common.error': 'Erreur',
    'common.success': 'Succès',
    'common.cancel': 'Annuler',
    'common.save': 'Enregistrer',
    'common.delete': 'Supprimer',
    'common.edit': 'Modifier',
    'common.back': 'Retour',
    'common.submit': 'Soumettre',
    'common.search': 'Rechercher',
    'common.filter': 'Filtrer',
    'common.reset': 'Réinitialiser',

    // Dashboard
    'dashboard.title': 'Dashboard',
    'dashboard.welcome': 'Bienvenue, {name} !',
    'dashboard.admin': 'Dashboard Admin',
    'dashboard.admin.subtitle': 'Gérez les utilisateurs, produits et demandes de boost',

    // Product submission
    'submit.title': 'Soumettre un produit',
    'submit.name': 'Nom du produit',
    'submit.description': 'Description',
    'submit.website': 'Site web',
    'submit.logo': 'URL du logo',
    'submit.category': 'Catégorie',
    'submit.status': 'Statut',
    'submit.country': 'Pays',
    'submit.tags': 'Tags',
    'submit.success': 'Produit soumis avec succès !',
    'submit.error': 'Erreur lors de la soumission',
    'submit.login_required': 'Connexion requise pour soumettre un produit',

    // Comments
    'comments.title': 'Commentaires',
    'comments.add': 'Ajouter un commentaire',
    'comments.placeholder': 'Partagez votre avis sur ce produit...',
    'comments.publish': 'Publier le commentaire',
    'comments.login_required': 'Connectez-vous pour laisser un commentaire',
    'comments.empty': 'Aucun commentaire pour le moment. Soyez le premier à donner votre avis !',
    'comments.success': 'Commentaire ajouté avec succès',
    'comments.error': 'Impossible d\'ajouter le commentaire'
  },
  en: {
    // Navigation
    'nav.home': 'Home',
    'nav.products': 'Products',
    'nav.about': 'About',
    'nav.login': 'Login',
    'nav.dashboard': 'Dashboard',
    'nav.profile': 'Profile',
    'nav.logout': 'Logout',
    
    // Hero Section
    'hero.title': 'Discover African Tech Innovations',
    'hero.subtitle': 'The platform that highlights the most promising African tech startups and products',
    'hero.cta': 'Discover Products',
    'hero.submit': 'Submit a Product',
    
    // Products
    'products.title': 'Products of the Day',
    'products.subtitle': 'Discover the most voted innovations by the African tech community',
    'products.search': 'Search for a product, country or description...',
    'products.category': 'Category',
    'products.all': 'All categories',
    'products.sort': 'Sort by',
    'products.votes': 'Most voted',
    'products.recent': 'Most recent',
    'products.visit': 'Visit website',
    'products.boost': 'Boost this product',
    'products.vote': 'Vote',
    'products.voted': 'Voted',
    
    // Auth
    'auth.title': 'Join AYOHUB',
    'auth.subtitle': 'Sign in or create an account to discover and promote African innovation',
    'auth.signin': 'Sign In',
    'auth.signup': 'Sign Up',
    'auth.email': 'Email',
    'auth.password': 'Password',
    'auth.fullname': 'Full Name',
    'auth.signin.button': 'Sign In',
    'auth.signup.button': 'Sign Up',
    
    // About
    'about.title': 'About AYOHUB',
    'about.mission.title': 'Our Mission',
    'about.mission.content': 'AYOHUB is the first platform dedicated to promoting and discovering African technological innovations. We believe that Africa is full of talent and revolutionary ideas that deserve to be highlighted.',
    'about.vision.title': 'Our Vision',
    'about.vision.content': 'To create a dynamic ecosystem where African entrepreneurs can showcase their innovations, receive constructive feedback and establish valuable connections with potential investors and partners.',
    'about.values.title': 'Our Values',
    'about.values.innovation': 'Innovation',
    'about.values.innovation.desc': 'We celebrate creativity and innovation in all its forms.',
    'about.values.community': 'Community',
    'about.values.community.desc': 'We build a supportive and collaborative community.',
    'about.values.excellence': 'Excellence',
    'about.values.excellence.desc': 'We strive for excellence in everything we do.',
    'about.team.title': 'Our Team',
    'about.contact.title': 'Contact Us',
    'about.contact.content': 'Have questions or want to collaborate with us? Feel free to contact <NAME_EMAIL>',

    // Common UI
    'common.loading': 'Loading...',
    'common.error': 'Error',
    'common.success': 'Success',
    'common.cancel': 'Cancel',
    'common.save': 'Save',
    'common.delete': 'Delete',
    'common.edit': 'Edit',
    'common.back': 'Back',
    'common.submit': 'Submit',
    'common.search': 'Search',
    'common.filter': 'Filter',
    'common.reset': 'Reset',

    // Dashboard
    'dashboard.title': 'Dashboard',
    'dashboard.welcome': 'Welcome, {name}!',
    'dashboard.admin': 'Admin Dashboard',
    'dashboard.admin.subtitle': 'Manage users, products and boost requests',

    // Product submission
    'submit.title': 'Submit a Product',
    'submit.name': 'Product Name',
    'submit.description': 'Description',
    'submit.website': 'Website',
    'submit.logo': 'Logo URL',
    'submit.category': 'Category',
    'submit.status': 'Status',
    'submit.country': 'Country',
    'submit.tags': 'Tags',
    'submit.success': 'Product submitted successfully!',
    'submit.error': 'Error submitting product',
    'submit.login_required': 'Login required to submit a product',

    // Comments
    'comments.title': 'Comments',
    'comments.add': 'Add a comment',
    'comments.placeholder': 'Share your thoughts about this product...',
    'comments.publish': 'Publish comment',
    'comments.login_required': 'Sign in to leave a comment',
    'comments.empty': 'No comments yet. Be the first to share your thoughts!',
    'comments.success': 'Comment added successfully',
    'comments.error': 'Unable to add comment'
  }
};

export const LanguageProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [language, setLanguage] = useState(() => {
    const saved = localStorage.getItem('ayohub_language');
    return saved || 'fr';
  });

  useEffect(() => {
    localStorage.setItem('ayohub_language', language);
  }, [language]);

  const t = (key: string): string => {
    return translations[language as keyof typeof translations]?.[key as keyof typeof translations.fr] || key;
  };

  const value = {
    language,
    setLanguage,
    t
  };

  return <LanguageContext.Provider value={value}>{children}</LanguageContext.Provider>;
};
