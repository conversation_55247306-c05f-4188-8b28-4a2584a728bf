-- Test Database Queries for Africa Tech Spotlight
-- Run these queries in Supabase SQL Editor to verify everything works

-- 1. Test basic products query (this should work now)
SELECT 
    id, 
    name, 
    description, 
    logo_url, 
    website_url, 
    country, 
    category, 
    status, 
    votes, 
    tags, 
    created_at, 
    user_id,
    maker
FROM public.products 
ORDER BY created_at DESC 
LIMIT 10;

-- 2. Test products with profile join (alternative approach)
SELECT 
    p.id,
    p.name,
    p.description,
    p.logo_url,
    p.website_url,
    p.country,
    p.category,
    p.status,
    p.votes,
    p.tags,
    p.created_at,
    p.user_id,
    p.maker,
    pr.full_name as profile_name,
    pr.email as profile_email
FROM public.products p
LEFT JOIN public.profiles pr ON p.user_id = pr.id
ORDER BY p.created_at DESC
LIMIT 10;

-- 3. Test single product query (for ProductDetails page)
SELECT *
FROM public.products
WHERE id = (SELECT id FROM public.products LIMIT 1);

-- 4. Test profile query for a specific user
SELECT full_name, email
FROM public.profiles
WHERE id = (SELECT user_id FROM public.products LIMIT 1);

-- 5. Test comments query
SELECT 
    c.*,
    pr.full_name,
    pr.email
FROM public.product_comments c
LEFT JOIN public.profiles pr ON c.user_id = pr.id
ORDER BY c.created_at DESC
LIMIT 10;

-- 6. Test votes query
SELECT 
    v.*,
    p.name as product_name,
    pr.full_name as voter_name
FROM public.votes v
LEFT JOIN public.products p ON v.product_id = p.id
LEFT JOIN public.profiles pr ON v.user_id = pr.id
LIMIT 10;

-- 7. Check RLS policies are working
-- This should return policy information
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual
FROM pg_policies 
WHERE schemaname = 'public' 
AND tablename IN ('products', 'profiles', 'product_comments', 'votes')
ORDER BY tablename, policyname;

-- 8. Check table structure
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns
WHERE table_schema = 'public'
AND table_name IN ('products', 'profiles', 'product_comments', 'votes')
ORDER BY table_name, ordinal_position;

-- 9. Test insert operations (simulate what the app does)
-- Note: Replace with actual user ID from auth.users
DO $$
DECLARE
    test_user_id UUID;
    test_product_id UUID;
BEGIN
    -- Get a test user ID (or create one if needed)
    SELECT id INTO test_user_id FROM auth.users LIMIT 1;
    
    IF test_user_id IS NULL THEN
        RAISE NOTICE 'No users found. Please register a user first.';
        RETURN;
    END IF;
    
    -- Test product insert
    INSERT INTO public.products (
        user_id, name, description, logo_url, website_url,
        category, country, status, tags, maker
    ) VALUES (
        test_user_id,
        'Test Product',
        'This is a test product for verification',
        'https://via.placeholder.com/100',
        'https://example.com',
        'Test',
        'Test Country',
        'MVP',
        ARRAY['test', 'verification'],
        'Test User'
    ) RETURNING id INTO test_product_id;
    
    RAISE NOTICE 'Test product created with ID: %', test_product_id;
    
    -- Test comment insert
    INSERT INTO public.product_comments (
        product_id, user_id, content
    ) VALUES (
        test_product_id,
        test_user_id,
        'This is a test comment'
    );
    
    RAISE NOTICE 'Test comment created';
    
    -- Test vote insert
    INSERT INTO public.votes (
        product_id, user_id
    ) VALUES (
        test_product_id,
        test_user_id
    );
    
    RAISE NOTICE 'Test vote created';
    
    -- Clean up test data
    DELETE FROM public.votes WHERE product_id = test_product_id;
    DELETE FROM public.product_comments WHERE product_id = test_product_id;
    DELETE FROM public.products WHERE id = test_product_id;
    
    RAISE NOTICE 'Test data cleaned up successfully';
    
EXCEPTION WHEN OTHERS THEN
    RAISE NOTICE 'Test failed with error: %', SQLERRM;
END $$;

-- 10. Final verification query
SELECT 
    'Database ready for application' as status,
    (SELECT COUNT(*) FROM public.products) as total_products,
    (SELECT COUNT(*) FROM public.profiles) as total_profiles,
    (SELECT COUNT(*) FROM public.product_comments) as total_comments,
    (SELECT COUNT(*) FROM public.votes) as total_votes;
