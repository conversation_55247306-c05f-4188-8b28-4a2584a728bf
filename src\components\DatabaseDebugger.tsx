import { useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';

const DatabaseDebugger = () => {
  const [results, setResults] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  const testQueries = async () => {
    setLoading(true);
    setResults([]);
    const testResults: any[] = [];

    // Test 1: Basic products query
    try {
      const { data, error } = await supabase
        .from('products')
        .select('*')
        .limit(5);
      
      testResults.push({
        test: 'Basic products query',
        success: !error,
        data: data?.length || 0,
        error: error?.message
      });
    } catch (err: any) {
      testResults.push({
        test: 'Basic products query',
        success: false,
        error: err.message
      });
    }

    // Test 2: Profiles query
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .limit(5);
      
      testResults.push({
        test: 'Basic profiles query',
        success: !error,
        data: data?.length || 0,
        error: error?.message
      });
    } catch (err: any) {
      testResults.push({
        test: 'Basic profiles query',
        success: false,
        error: err.message
      });
    }

    // Test 3: Comments query
    try {
      const { data, error } = await supabase
        .from('product_comments')
        .select('*')
        .limit(5);
      
      testResults.push({
        test: 'Basic comments query',
        success: !error,
        data: data?.length || 0,
        error: error?.message
      });
    } catch (err: any) {
      testResults.push({
        test: 'Basic comments query',
        success: false,
        error: err.message
      });
    }

    // Test 4: Votes query
    try {
      const { data, error } = await supabase
        .from('votes')
        .select('*')
        .limit(5);
      
      testResults.push({
        test: 'Basic votes query',
        success: !error,
        data: data?.length || 0,
        error: error?.message
      });
    } catch (err: any) {
      testResults.push({
        test: 'Basic votes query',
        success: false,
        error: err.message
      });
    }

    // Test 5: Auth user
    try {
      const { data: { user }, error } = await supabase.auth.getUser();
      
      testResults.push({
        test: 'Auth user check',
        success: !error,
        data: user ? 'User logged in' : 'No user',
        error: error?.message
      });
    } catch (err: any) {
      testResults.push({
        test: 'Auth user check',
        success: false,
        error: err.message
      });
    }

    setResults(testResults);
    setLoading(false);
  };

  return (
    <Card className="m-4">
      <CardHeader>
        <CardTitle>Database Connection Debugger</CardTitle>
      </CardHeader>
      <CardContent>
        <Button onClick={testQueries} disabled={loading}>
          {loading ? 'Testing...' : 'Test Database Queries'}
        </Button>
        
        {results.length > 0 && (
          <div className="mt-4 space-y-2">
            {results.map((result, index) => (
              <Alert key={index} variant={result.success ? "default" : "destructive"}>
                <AlertDescription>
                  <strong>{result.test}:</strong> {' '}
                  {result.success ? (
                    <span className="text-green-600">
                      ✅ Success ({result.data} records)
                    </span>
                  ) : (
                    <span className="text-red-600">
                      ❌ Failed - {result.error}
                    </span>
                  )}
                </AlertDescription>
              </Alert>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default DatabaseDebugger;
