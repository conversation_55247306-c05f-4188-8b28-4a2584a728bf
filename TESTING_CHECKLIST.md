# Africa Tech Spotlight - Testing Checklist

Use this checklist to verify all functionality is working correctly after local deployment.

## ✅ Pre-Testing Setup

### Environment Verification
- [ ] Development server running on http://localhost:3000
- [ ] No console errors in browser developer tools
- [ ] Supabase connection working (check Network tab for API calls)
- [ ] Environment variables loaded correctly

### Database Setup
- [ ] All migrations applied successfully
- [ ] Sample data inserted (run setup-test-data.sql)
- [ ] RLS policies active and working

## ✅ Core Functionality Testing

### 1. Homepage and Navigation
- [ ] Homepage loads without errors
- [ ] Hero section displays correctly
- [ ] Language selector works (FR/EN toggle)
- [ ] Navigation menu responsive on mobile
- [ ] All navigation links functional

### 2. User Authentication
#### Registration
- [ ] Navigate to /auth or /register
- [ ] Register new account with valid email
- [ ] Email verification process (if enabled)
- [ ] Successful registration redirects to dashboard

#### Login
- [ ] Login with registered credentials
- [ ] "Remember me" functionality
- [ ] Error handling for invalid credentials
- [ ] Successful login redirects appropriately

#### Profile Management
- [ ] Access user profile/dashboard
- [ ] View user information
- [ ] Update profile information (if implemented)
- [ ] Logout functionality works

### 3. Product Management
#### Viewing Products
- [ ] Products list loads on homepage
- [ ] Product cards display correctly
- [ ] Product details page accessible (/product/:id)
- [ ] Product images, descriptions, and metadata visible
- [ ] Boosted products highlighted (if any)

#### Product Submission
- [ ] Access product submission form
- [ ] Fill out all required fields:
  - [ ] Product name
  - [ ] Description
  - [ ] Website URL
  - [ ] Logo URL
  - [ ] Category selection
  - [ ] Country selection
  - [ ] Status (MVP/Beta/Launch)
  - [ ] Tags
- [ ] Form validation works
- [ ] Successful submission creates product
- [ ] Error handling for invalid data

#### Search and Filtering
- [ ] Search functionality works
- [ ] Category filtering works
- [ ] Sort by votes/recent works
- [ ] Filter reset functionality
- [ ] No results state displays correctly

### 4. Voting System
#### Voting Functionality
- [ ] Vote button visible for logged-in users
- [ ] Click to vote increases count
- [ ] Click again to remove vote (toggle)
- [ ] Vote count updates in real-time
- [ ] Voting restricted to authenticated users

#### Vote Security
- [ ] Cannot vote multiple times on same product
- [ ] Vote state persists across page reloads
- [ ] Database vote count matches display

### 5. Comments System
#### Adding Comments
- [ ] Comment form visible on product details
- [ ] Submit comment with valid content
- [ ] Comment appears immediately
- [ ] Character limit enforced (if any)
- [ ] Login required for commenting

#### Viewing Comments
- [ ] Comments display correctly
- [ ] Comment author and timestamp visible
- [ ] Comments sorted by date
- [ ] Empty state when no comments

#### Comment Interactions
- [ ] Like/unlike comments
- [ ] Like count updates correctly
- [ ] User can see their liked comments

## ✅ Admin Dashboard Testing

### Admin Access Setup
- [ ] Register admin account
- [ ] Grant admin role via database:
  ```sql
  UPDATE profiles SET admin_role = true WHERE email = '<EMAIL>';
  ```
- [ ] Verify admin role in profiles table
- [ ] Admin menu appears in navigation

### Admin Dashboard Features
#### Statistics Overview
- [ ] Navigate to /admin
- [ ] Dashboard loads without errors
- [ ] User count displays correctly
- [ ] Product count displays correctly
- [ ] Boost statistics visible
- [ ] Pending requests count accurate

#### User Management
- [ ] View all registered users
- [ ] User information displays correctly
- [ ] Grant/revoke admin roles
- [ ] Admin role changes persist
- [ ] Cannot remove own admin role

#### Boost Request Management
- [ ] View boost requests (if any)
- [ ] Approve boost requests
- [ ] Reject boost requests
- [ ] Status updates correctly
- [ ] Approved boosts activate properly

### Admin Security
- [ ] Non-admin users cannot access /admin
- [ ] Admin-only features hidden from regular users
- [ ] RLS policies protect admin operations

## ✅ Internationalization Testing

### Language Switching
- [ ] French (FR) language works
- [ ] English (EN) language works
- [ ] Language preference persists
- [ ] All UI elements translated
- [ ] No missing translation keys

### Content Verification
- [ ] Navigation menu translated
- [ ] Form labels translated
- [ ] Error messages translated
- [ ] Success messages translated
- [ ] Product submission form translated

## ✅ Responsive Design Testing

### Mobile Testing
- [ ] Homepage responsive on mobile
- [ ] Navigation menu collapses properly
- [ ] Forms usable on mobile
- [ ] Product cards stack correctly
- [ ] Admin dashboard mobile-friendly

### Tablet Testing
- [ ] Layout adapts to tablet size
- [ ] Touch interactions work
- [ ] Content readable and accessible

### Desktop Testing
- [ ] Full desktop layout works
- [ ] All features accessible
- [ ] Optimal use of screen space

## ✅ Performance Testing

### Loading Performance
- [ ] Initial page load under 3 seconds
- [ ] Product list loads quickly
- [ ] Images load progressively
- [ ] No unnecessary API calls

### Error Handling
- [ ] Network errors handled gracefully
- [ ] Database errors show user-friendly messages
- [ ] 404 pages work correctly
- [ ] Form validation errors clear

## ✅ Security Testing

### Authentication Security
- [ ] Protected routes require login
- [ ] JWT tokens handled securely
- [ ] Session management works
- [ ] Logout clears session

### Data Security
- [ ] RLS policies prevent unauthorized access
- [ ] Users can only edit own content
- [ ] Admin operations properly restricted
- [ ] SQL injection protection

## ✅ Browser Compatibility

### Modern Browsers
- [ ] Chrome (latest)
- [ ] Firefox (latest)
- [ ] Safari (latest)
- [ ] Edge (latest)

### Features
- [ ] JavaScript functionality
- [ ] CSS styling consistent
- [ ] Local storage works
- [ ] API calls successful

## 🐛 Common Issues and Solutions

### Database Connection Issues
**Problem:** Products not loading, authentication failing
**Solution:** 
- Check .env file configuration
- Verify Supabase project status
- Check browser console for errors

### Admin Access Issues
**Problem:** Cannot access admin dashboard
**Solution:**
- Verify admin_role = true in database
- Clear browser cache
- Check RLS policies

### Voting Not Working
**Problem:** Vote button not responding
**Solution:**
- Ensure user is logged in
- Check vote table permissions
- Verify RLS policies

### Translation Issues
**Problem:** Text not translating
**Solution:**
- Check translation keys in LanguageContext
- Verify component imports useLanguage hook
- Check browser console for errors

## 📝 Test Results

Date: ___________
Tester: ___________

### Summary
- [ ] All core features working
- [ ] Admin dashboard functional
- [ ] No critical bugs found
- [ ] Ready for production deployment

### Notes
_Add any additional observations or issues found during testing_

---

**Next Steps After Testing:**
1. Fix any identified issues
2. Add additional test data if needed
3. Configure production environment
4. Set up monitoring and analytics
5. Deploy to production hosting
