
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useLanguage } from "@/contexts/LanguageContext";
import { Lightbulb, Users, Award, Mail } from "lucide-react";

const About = () => {
  const { t } = useLanguage();

  return (
    <div className="min-h-screen bg-background">
      <Header />
      <main className="py-16">
        <div className="container mx-auto px-4">
          {/* Hero Section */}
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              <span className="bg-gradient-sunset bg-clip-text text-transparent">
                {t('about.title')}
              </span>
            </h1>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              La plateforme qui connecte les innovateurs africains avec le monde
            </p>
          </div>

          {/* Mission, Vision, Values */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
            <Card className="shadow-elegant">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Lightbulb className="w-6 h-6 text-primary" />
                  <span>{t('about.mission.title')}</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground leading-relaxed">
                  {t('about.mission.content')}
                </p>
              </CardContent>
            </Card>

            <Card className="shadow-elegant">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Users className="w-6 h-6 text-primary" />
                  <span>{t('about.vision.title')}</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground leading-relaxed">
                  {t('about.vision.content')}
                </p>
              </CardContent>
            </Card>

            <Card className="shadow-elegant">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Award className="w-6 h-6 text-primary" />
                  <span>{t('about.values.title')}</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <h4 className="font-semibold text-primary">{t('about.values.innovation')}</h4>
                    <p className="text-sm text-muted-foreground">{t('about.values.innovation.desc')}</p>
                  </div>
                  <div>
                    <h4 className="font-semibold text-primary">{t('about.values.community')}</h4>
                    <p className="text-sm text-muted-foreground">{t('about.values.community.desc')}</p>
                  </div>
                  <div>
                    <h4 className="font-semibold text-primary">{t('about.values.excellence')}</h4>
                    <p className="text-sm text-muted-foreground">{t('about.values.excellence.desc')}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Statistics */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-16">
            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="text-3xl font-bold text-primary mb-2">500+</div>
                <p className="text-sm text-muted-foreground">Produits répertoriés</p>
              </CardContent>
            </Card>
            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="text-3xl font-bold text-accent mb-2">54</div>
                <p className="text-sm text-muted-foreground">Pays africains</p>
              </CardContent>
            </Card>
            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="text-3xl font-bold text-green-600 mb-2">10K+</div>
                <p className="text-sm text-muted-foreground">Membres actifs</p>
              </CardContent>
            </Card>
            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="text-3xl font-bold text-orange-600 mb-2">€2M+</div>
                <p className="text-sm text-muted-foreground">Investissements générés</p>
              </CardContent>
            </Card>
          </div>

          {/* Team Section */}
          <div className="mb-16">
            <h2 className="text-3xl font-bold text-center mb-8">{t('about.team.title')}</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <Card className="text-center">
                <CardContent className="pt-6">
                  <img 
                    src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face" 
                    alt="Kwame Asante"
                    className="w-24 h-24 rounded-full mx-auto mb-4"
                  />
                  <h3 className="font-semibold text-lg">Kwame Asante</h3>
                  <p className="text-muted-foreground mb-2">Fondateur & CEO</p>
                  <Badge variant="outline">Ghana</Badge>
                </CardContent>
              </Card>
              
              <Card className="text-center">
                <CardContent className="pt-6">
                  <img 
                    src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face" 
                    alt="Aminata Diallo"
                    className="w-24 h-24 rounded-full mx-auto mb-4"
                  />
                  <h3 className="font-semibold text-lg">Aminata Diallo</h3>
                  <p className="text-muted-foreground mb-2">CTO</p>
                  <Badge variant="outline">Sénégal</Badge>
                </CardContent>
              </Card>
              
              <Card className="text-center">
                <CardContent className="pt-6">
                  <img 
                    src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face" 
                    alt="Chinedu Okoro"
                    className="w-24 h-24 rounded-full mx-auto mb-4"
                  />
                  <h3 className="font-semibold text-lg">Chinedu Okoro</h3>
                  <p className="text-muted-foreground mb-2">Head of Community</p>
                  <Badge variant="outline">Nigeria</Badge>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Contact Section */}
          <Card className="shadow-elegant">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2 text-center justify-center">
                <Mail className="w-6 h-6 text-primary" />
                <span>{t('about.contact.title')}</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="text-center">
              <p className="text-muted-foreground mb-4">
                {t('about.contact.content')}
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Badge variant="outline" className="text-primary">
                  📧 <EMAIL>
                </Badge>
                <Badge variant="outline" className="text-primary">
                  🐦 @ayohub_africa
                </Badge>
                <Badge variant="outline" className="text-primary">
                  💼 LinkedIn: AYOHUB
                </Badge>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default About;
