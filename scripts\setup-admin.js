#!/usr/bin/env node

/**
 * AYOHUB Admin Setup Script
 * 
 * This script helps set up admin access for the AYOHUB platform.
 * Run this script after setting up your Supabase project.
 */

const fs = require('fs');
const path = require('path');

console.log('🚀 AYOHUB Admin Setup Script');
console.log('================================\n');

// Check if we're in the right directory
const packageJsonPath = path.join(process.cwd(), 'package.json');
if (!fs.existsSync(packageJsonPath)) {
  console.error('❌ Error: package.json not found. Please run this script from the project root directory.');
  process.exit(1);
}

// Read package.json to verify this is the AYOHUB project
try {
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  if (!packageJson.name || !packageJson.name.includes('ayohub')) {
    console.warn('⚠️  Warning: This doesn\'t appear to be the AYOHUB project.');
  }
} catch (error) {
  console.error('❌ Error reading package.json:', error.message);
  process.exit(1);
}

console.log('📋 Admin Setup Checklist:');
console.log('==========================\n');

console.log('1. ✅ Database Setup');
console.log('   - Copy the contents of database/admin_setup.sql');
console.log('   - Run it in your Supabase SQL editor');
console.log('   - This will create the admin user and set up permissions\n');

console.log('2. ✅ Environment Variables');
console.log('   - Add these to your .env.local file:');
console.log('   VITE_ADMIN_EMAIL=<EMAIL>');
console.log('   VITE_ENABLE_ADMIN_FEATURES=true\n');

console.log('3. ✅ Admin Credentials');
console.log('   - Email: <EMAIL>');
console.log('   - Password: AyohubAdmin2024!');
console.log('   - These are set up automatically by the SQL script\n');

console.log('4. ✅ Access Admin Dashboard');
console.log('   - Start your development server: npm run dev');
console.log('   - Login with admin credentials');
console.log('   - Navigate to /admin or click the Admin button in the header\n');

console.log('🔒 Security Notes:');
console.log('==================');
console.log('- Change the default admin password after first login');
console.log('- Only grant admin privileges to trusted users');
console.log('- Admin actions are logged in the audit trail');
console.log('- Regular users cannot access admin features\n');

console.log('🛠️  Troubleshooting:');
console.log('====================');
console.log('- If admin button doesn\'t appear: Check database admin_role field');
console.log('- If access denied: Verify RLS policies are applied');
console.log('- If login fails: Check Supabase auth configuration');
console.log('- For help: Check ADMIN_SETUP_GUIDE.md\n');

// Check if SQL file exists
const sqlFilePath = path.join(process.cwd(), 'database', 'admin_setup.sql');
if (fs.existsSync(sqlFilePath)) {
  console.log('✅ SQL setup file found at: database/admin_setup.sql');
} else {
  console.log('❌ SQL setup file not found. Please ensure database/admin_setup.sql exists.');
}

// Check if guide exists
const guidePath = path.join(process.cwd(), 'ADMIN_SETUP_GUIDE.md');
if (fs.existsSync(guidePath)) {
  console.log('✅ Setup guide found at: ADMIN_SETUP_GUIDE.md');
} else {
  console.log('❌ Setup guide not found. Please ensure ADMIN_SETUP_GUIDE.md exists.');
}

console.log('\n🎉 Setup Complete!');
console.log('==================');
console.log('Follow the steps above to complete your admin setup.');
console.log('For detailed instructions, see ADMIN_SETUP_GUIDE.md\n');

// Create a simple verification script
const verifyScript = `
-- Run this query in Supabase to verify admin setup
SELECT 
  'Admin Users' as type,
  email,
  full_name,
  admin_role,
  created_at
FROM profiles 
WHERE admin_role = true

UNION ALL

SELECT 
  'Total Stats' as type,
  CAST(COUNT(*) as TEXT) as email,
  'Total Users' as full_name,
  NULL as admin_role,
  NULL as created_at
FROM profiles;
`;

const verifyPath = path.join(process.cwd(), 'verify-admin-setup.sql');
fs.writeFileSync(verifyPath, verifyScript);
console.log('📝 Created verification script: verify-admin-setup.sql');
console.log('   Run this in Supabase to verify your setup\n');

console.log('Happy coding! 🚀');
