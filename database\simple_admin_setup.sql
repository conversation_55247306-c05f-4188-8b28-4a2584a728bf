-- AYOHUB Simple Admin Setup
-- Run this script in Supabase SQL Editor

-- 1. Add admin_role column to profiles table
ALTER TABLE profiles 
ADD COLUMN IF NOT EXISTS admin_role BOOLEAN DEFAULT FALSE;

-- 2. Enable Row Level Security on all tables
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE votes ENABLE ROW LEVEL SECURITY;
ALTER TABLE product_comments ENABLE ROW LEVEL SECURITY;

-- 3. Create admin policies for profiles table
CREATE POLICY "Ad<PERSON> can view all profiles" ON profiles
FOR SELECT USING (
  auth.uid() IN (
    SELECT id FROM profiles WHERE admin_role = true
  )
);

CREATE POLICY "Admins can update all profiles" ON profiles
FOR UPDATE USING (
  auth.uid() IN (
    SELECT id FROM profiles WHERE admin_role = true
  )
);

-- 4. Create admin policies for products table
CREATE POLICY "Admins can manage all products" ON products
FOR ALL USING (
  auth.uid() IN (
    SELECT id FROM profiles WHERE admin_role = true
  )
);

-- 5. Create admin policies for votes table
CREATE POLICY "Admins can manage votes" ON votes
FOR ALL USING (
  auth.uid() IN (
    SELECT id FROM profiles WHERE admin_role = true
  )
);

-- 6. Create admin policies for comments table
CREATE POLICY "Admins can manage comments" ON product_comments
FOR ALL USING (
  auth.uid() IN (
    SELECT id FROM profiles WHERE admin_role = true
  )
);

-- 7. Create admin helper function
CREATE OR REPLACE FUNCTION is_admin()
RETURNS BOOLEAN
LANGUAGE sql
SECURITY DEFINER
STABLE
AS $$
  SELECT EXISTS(
    SELECT 1 FROM profiles 
    WHERE id = auth.uid() AND admin_role = true
  );
$$;

-- 8. Create function to promote user to admin
CREATE OR REPLACE FUNCTION promote_to_admin(user_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  UPDATE profiles 
  SET 
    admin_role = true,
    role = 'admin',
    updated_at = NOW()
  WHERE id = user_id;

  RETURN FOUND;
END;
$$;

-- 9. Create admin statistics view
CREATE OR REPLACE VIEW admin_stats AS
SELECT 
  (SELECT COUNT(*) FROM profiles) as total_users,
  (SELECT COUNT(*) FROM profiles WHERE admin_role = true) as admin_users,
  (SELECT COUNT(*) FROM products) as total_products,
  (SELECT COUNT(*) FROM votes) as total_votes;

-- Grant access to admin stats view
GRANT SELECT ON admin_stats TO authenticated;

-- 10. Success message
SELECT 'Admin setup completed! Now create an admin user by:' as message
UNION ALL
SELECT '1. Register normally at /auth with email: <EMAIL>' as message
UNION ALL  
SELECT '2. Then run: UPDATE profiles SET admin_role = true WHERE email = ''<EMAIL>'';' as message;
