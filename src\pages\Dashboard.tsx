
import { useAuth } from "@/contexts/AuthContext";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import UserProfile from "@/components/UserProfile";
import { Navigate } from "react-router-dom";
import { User } from "@/types/user";

const Dashboard = () => {
  const { user, isLoading } = useAuth();

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-muted-foreground">Chargement...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return <Navigate to="/auth" replace />;
  }

  // Create a mock user profile based on Supabase user data
  const userProfile: User = {
    id: user.id,
    name: user.email?.split('@')[0] || 'Utilisateur',
    email: user.email || '',
    avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face",
    bio: "Membre de la communauté AYOHUB",
    role: "Supporter",
    country: "France",
    joinedAt: new Date().toISOString(),
    productsLaunched: [],
    productsUpvoted: [],
    followers: 0,
    following: 0
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />
      <main className="py-16">
        <div className="container mx-auto px-4">
          <div className="mb-8">
            <h1 className="text-3xl font-bold bg-gradient-sunset bg-clip-text text-transparent">
              Dashboard
            </h1>
            <p className="text-muted-foreground mt-2">
              Bienvenue, {userProfile.name} !
            </p>
          </div>
          
          <UserProfile user={userProfile} isOwnProfile={true} />
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default Dashboard;
