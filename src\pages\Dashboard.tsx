
import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useLanguage } from "@/contexts/LanguageContext";
import { supabase } from "@/integrations/supabase/client";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import UserProfile from "@/components/UserProfile";
import { Navigate } from "react-router-dom";
import { User } from "@/types/user";

const Dashboard = () => {
  const { user, isLoading } = useAuth();
  const { t } = useLanguage();
  const [userProfile, setUserProfile] = useState<User | null>(null);
  const [profileLoading, setProfileLoading] = useState(true);

  useEffect(() => {
    if (user) {
      loadUserProfile();
    }
  }, [user]);

  const loadUserProfile = async () => {
    try {
      setProfileLoading(true);

      // Get user profile from profiles table
      const { data: profile, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user!.id)
        .single();

      if (error && error.code !== 'PGRST116') {
        console.error('Error loading profile:', error);
      }

      // Create user profile object
      const userProfileData: User = {
        id: user!.id,
        name: profile?.full_name || user!.email?.split('@')[0] || 'Utilisateur',
        email: user!.email || '',
        avatar: profile?.avatar_url || "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face",
        bio: profile?.bio || "Membre de la communauté AYOHUB",
        role: profile?.admin_role ? "Maker" : "Supporter",
        country: profile?.country || "Non spécifié",
        joinedAt: profile?.created_at || new Date().toISOString(),
        productsLaunched: [],
        productsUpvoted: [],
        followers: 0,
        following: 0
      };

      setUserProfile(userProfileData);
    } catch (error) {
      console.error('Error loading user profile:', error);
    } finally {
      setProfileLoading(false);
    }
  };

  if (isLoading || profileLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-muted-foreground">{t('common.loading')}</p>
        </div>
      </div>
    );
  }

  if (!user || !userProfile) {
    return <Navigate to="/auth" replace />;
  }

  return (
    <div className="min-h-screen bg-background">
      <Header />
      <main className="py-16">
        <div className="container mx-auto px-4">
          <div className="mb-8">
            <h1 className="text-3xl font-bold bg-gradient-sunset bg-clip-text text-transparent">
              {t('dashboard.title')}
            </h1>
            <p className="text-muted-foreground mt-2">
              Bienvenue, {userProfile.name} !
            </p>
          </div>
          
          <UserProfile user={userProfile} isOwnProfile={true} />
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default Dashboard;
