# 🔒 Sécurité Admin AYOHUB

## 🛡️ **Principe de Sécurité**

**Pa<PERSON> d<PERSON>, TOUS les utilisateurs ont `admin_role = false`**

Cette approche garantit que :
- ✅ Aucun utilisateur n'obtient accidentellement des privilèges admin
- ✅ Vous contrôlez manuellement qui devient administrateur
- ✅ Sécurité maximale de la plateforme

## 👥 **Gestion des Administrateurs**

### **Activer un Admin**
```sql
UPDATE profiles 
SET admin_role = true, role = 'admin' 
WHERE email = '<EMAIL>';
```

### **Désactiver un Admin**
```sql
UPDATE profiles 
SET admin_role = false, role = 'supporter' 
WHERE email = '<EMAIL>';
```

### **Voir tous les Admins**
```sql
SELECT email, admin_role, role, created_at 
FROM profiles 
WHERE admin_role = true;
```

## 🎯 **Fonctions Utiles**

### **Promouvoir un Utilisateur**
```sql
SELECT promote_user_to_admin('<EMAIL>');
```

### **Révoquer les Privilèges**
```sql
SELECT revoke_admin_privileges('<EMAIL>');
```

## ⚠️ **Mesures de Sécurité**

### **1. Principe du Moindre Privilège**
- Seuls les utilisateurs de confiance deviennent admin
- Révoquez les privilèges quand ils ne sont plus nécessaires

### **2. Audit des Admins**
```sql
-- Vérifiez régulièrement qui sont vos admins
SELECT 
  email,
  admin_role,
  role,
  created_at,
  updated_at
FROM profiles 
WHERE admin_role = true
ORDER BY updated_at DESC;
```

### **3. Urgence - Révoquer Tous les Admins**
```sql
-- ⚠️ SEULEMENT EN CAS D'URGENCE
UPDATE profiles 
SET admin_role = false, role = 'supporter' 
WHERE admin_role = true;
```

## 📋 **Checklist de Sécurité**

- [ ] Vérifiez régulièrement la liste des admins
- [ ] Révoquez les privilèges des anciens admins
- [ ] Utilisez des mots de passe forts pour les comptes admin
- [ ] Surveillez les actions admin dans les logs
- [ ] Ne partagez jamais les identifiants admin

## 🔍 **Surveillance**

### **Compter les Admins**
```sql
SELECT 
  COUNT(*) as nombre_admins,
  COUNT(*) FILTER (WHERE admin_role = true) as admins_actifs,
  COUNT(*) FILTER (WHERE admin_role = false) as utilisateurs_normaux
FROM profiles;
```

### **Dernière Activité Admin**
```sql
SELECT 
  email,
  admin_role,
  updated_at as derniere_modification
FROM profiles 
WHERE admin_role = true
ORDER BY updated_at DESC;
```

## 🚨 **En Cas de Problème**

### **Compte Admin Compromis**
1. Révoquez immédiatement les privilèges :
   ```sql
   UPDATE profiles SET admin_role = false WHERE email = '<EMAIL>';
   ```

2. Changez le mot de passe du compte

3. Vérifiez les actions récentes dans les logs

### **Perte d'Accès Admin**
1. Connectez-vous à Supabase directement
2. Exécutez le script de promotion :
   ```sql
   UPDATE profiles SET admin_role = true WHERE email = '<EMAIL>';
   ```

## 📊 **Bonnes Pratiques**

1. **Minimum d'Admins** : N'ayez que le nombre nécessaire d'administrateurs
2. **Rotation** : Changez régulièrement les mots de passe admin
3. **Documentation** : Tenez une liste des admins autorisés
4. **Monitoring** : Surveillez les actions admin
5. **Backup** : Sauvegardez régulièrement la base de données

## 🎯 **Résumé**

- 🔒 **Sécurisé par défaut** : `admin_role = false`
- 👑 **Contrôle manuel** : Vous décidez qui devient admin
- 🛡️ **Révocable** : Vous pouvez retirer les privilèges à tout moment
- 📊 **Traçable** : Toutes les modifications sont enregistrées

**Votre plateforme AYOHUB est maintenant sécurisée !** 🚀
