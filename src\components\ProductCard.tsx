import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardFooter, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Product } from "@/types/product";
import { useAuth } from "@/contexts/AuthContext";
import { useLanguage } from "@/contexts/LanguageContext";
import { useToast } from "@/hooks/use-toast";
import VoteService from "@/services/voteService";
import BoostRequestDialog from "./BoostRequestDialog";
import { ChevronUp, Shield, Users, Clock, Zap, Star, MessageCircle } from "lucide-react";
import { useNavigate } from "react-router-dom";

interface ProductCardProps {
  product: Product;
  onVote: (productId: string) => void;
}

const ProductCard = ({ product, onVote }: ProductCardProps) => {
  const { user } = useAuth();
  const { t } = useLanguage();
  const { toast } = useToast();
  const navigate = useNavigate();
  const voteService = VoteService.getInstance();
  
  const [votes, setVotes] = useState(product.votes);
  const [hasVoted, setHasVoted] = useState(false);
  const [credibility, setCredibility] = useState(voteService.getVoteCredibility(product.id));

  useEffect(() => {
    if (user) {
      setHasVoted(voteService.hasUserVoted(product.id, user.id));
    }
    setVotes(voteService.getVoteCount(product.id) + product.votes);
    setCredibility(voteService.getVoteCredibility(product.id));
  }, [product.id, user]);

  const handleVote = async () => {
    if (!user) {
      toast({
        variant: "destructive",
        title: "Connexion requise",
        description: "Vous devez être connecté pour voter",
      });
      return;
    }

    // Create a mock user object with required properties for the vote service
    const mockUser = {
      id: user.id,
      name: user.email || 'User',
      email: user.email || '',
      avatar: '',
      bio: '',
      role: 'Supporter' as const,
      country: '',
      joinedAt: '',
      productsLaunched: [],
      productsUpvoted: [],
      followers: 0,
      following: 0
    };

    const result = await voteService.vote(product.id, mockUser);
    
    if (result.success) {
      setHasVoted(true);
      setVotes(prev => prev + 1);
      setCredibility(voteService.getVoteCredibility(product.id));
      onVote(product.id);
      
      toast({
        title: "Vote enregistré",
        description: `${voteService.getRemainingDailyVotes(user.id)} votes restants aujourd'hui`,
      });
    } else {
      toast({
        variant: "destructive",
        title: "Vote impossible",
        description: result.message,
      });
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'MVP':
        return 'bg-accent/20 text-accent border-accent/30';
      case 'Beta':
        return 'bg-primary/20 text-primary border-primary/30';
      case 'Lancement':
        return 'bg-green-500/20 text-green-700 border-green-500/30';
      default:
        return 'bg-muted text-muted-foreground';
    }
  };

  const getCredibilityColor = (score: number) => {
    if (score >= 80) return "text-green-600";
    if (score >= 60) return "text-yellow-600";
    return "text-red-600";
  };

  // Simulate boosted status (in real app, this would come from database)
  const isBoosted = Math.random() > 0.7; // 30% chance to be boosted for demo

  const handleProductClick = () => {
    navigate(`/product/${product.id}`);
  };

  return (
    <TooltipProvider>
      <Card className={`group hover:shadow-warm transition-all duration-300 hover:-translate-y-1 bg-card border-border/50 ${
        isBoosted ? 'ring-2 ring-primary/50 bg-gradient-to-br from-primary/5 to-transparent' : ''
      }`}>
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex items-center space-x-3">
              <img 
                src={product.logo} 
                alt={`${product.name} logo`}
                className="w-12 h-12 rounded-lg object-cover border border-border"
              />
              <div>
                <div className="flex items-center space-x-2">
                  <h3 
                    className="font-semibold text-lg text-foreground group-hover:text-primary transition-colors cursor-pointer"
                    onClick={handleProductClick}
                  >
                    {product.name}
                  </h3>
                  {isBoosted && (
                    <Tooltip>
                      <TooltipTrigger>
                        <Badge className="bg-gradient-to-r from-primary to-accent text-white text-xs px-2 py-1">
                          <Zap className="w-3 h-3 mr-1" />
                          Boosted
                        </Badge>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Ce produit est mis en vedette</p>
                      </TooltipContent>
                    </Tooltip>
                  )}
                </div>
                <p className="text-sm text-muted-foreground">par {product.maker}</p>
              </div>
            </div>
            
            <div className="flex flex-col items-center space-y-2">
              <Button
                variant="vote"
                size="sm"
                onClick={handleVote}
                disabled={hasVoted || !user}
                className={`flex flex-col items-center px-3 py-2 h-auto min-w-[60px] ${
                  hasVoted ? 'bg-primary/10 border-primary text-primary' : ''
                }`}
              >
                <ChevronUp className={`w-4 h-4 ${hasVoted ? 'text-primary' : ''}`} />
                <span className={`text-xs font-medium ${hasVoted ? 'text-primary' : ''}`}>
                  {votes}
                </span>
              </Button>
              
              <Tooltip>
                <TooltipTrigger>
                  <div className="flex items-center space-x-1 text-xs">
                    <Shield className={`w-3 h-3 ${getCredibilityColor(credibility.credibilityScore)}`} />
                    <span className={getCredibilityColor(credibility.credibilityScore)}>
                      {credibility.credibilityScore}%
                    </span>
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  <div className="text-xs space-y-1">
                    <p>Score de crédibilité: {credibility.credibilityScore}%</p>
                    <p>Votants uniques: {credibility.uniqueVoters}</p>
                    <p>Total votes: {credibility.totalVotes}</p>
                  </div>
                </TooltipContent>
              </Tooltip>
            </div>
          </div>
        </CardHeader>

        <CardContent className="py-2">
          <p 
            className="text-muted-foreground text-sm leading-relaxed mb-4 cursor-pointer hover:text-foreground transition-colors"
            onClick={handleProductClick}
          >
            {product.description}
          </p>
          
          <div className="flex flex-wrap gap-2 mb-4">
            <Badge variant="secondary" className="text-xs">
              📍 {product.country}
            </Badge>
            <Badge variant="outline" className={getStatusColor(product.status)}>
              {product.status}
            </Badge>
            <Badge variant="outline" className="text-xs">
              {product.category}
            </Badge>
          </div>

          <div className="flex flex-wrap gap-1">
            {product.tags.map((tag, index) => (
              <span 
                key={index}
                className="text-xs px-2 py-1 bg-muted rounded-md text-muted-foreground"
              >
                #{tag}
              </span>
            ))}
          </div>

          {user && (
            <div className="mt-4 pt-4 border-t border-border/50">
              <div className="flex items-center justify-between text-xs text-muted-foreground">
                <div className="flex items-center space-x-1">
                  <Users className="w-3 h-3" />
                  <span>Votants uniques: {credibility.uniqueVoters}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Clock className="w-3 h-3" />
                  <span>Votes restants: {VoteService.getInstance().getRemainingDailyVotes(user.id)}</span>
                </div>
              </div>
            </div>
          )}
        </CardContent>

        <CardFooter className="pt-2 space-x-2">
          <Button 
            variant="outline" 
            className="flex-1" 
            onClick={() => window.open(product.website, '_blank')}
          >
            {t('products.visit')}
          </Button>
          
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={handleProductClick}
            className="px-3"
          >
            <MessageCircle className="w-4 h-4" />
          </Button>
          
          {user && (
            <BoostRequestDialog productId={product.id} productName={product.name}>
              <Button variant="secondary" size="sm" className="px-3">
                <Star className="w-4 h-4" />
              </Button>
            </BoostRequestDialog>
          )}
        </CardFooter>
      </Card>
    </TooltipProvider>
  );
};

export default ProductCard;
