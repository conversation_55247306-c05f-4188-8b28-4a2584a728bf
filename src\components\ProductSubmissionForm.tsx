
import { useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useLanguage } from "@/contexts/LanguageContext";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import FeaturedWidget from "./FeaturedWidget";
import { Plus, X, Rocket, Code } from "lucide-react";

const ProductSubmissionForm = () => {
  const { user } = useAuth();
  const { t } = useLanguage();
  const { toast } = useToast();
  
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    website: '',
    logo: '',
    category: '',
    status: 'MVP' as 'MVP' | 'Beta' | 'Lancement',
    country: '',
    tags: [] as string[]
  });
  
  const [currentTag, setCurrentTag] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!user) {
      toast({
        variant: "destructive",
        title: "Connexion requise",
        description: "Vous devez être connecté pour soumettre un produit",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      const { data, error } = await supabase
        .from('products')
        .insert([
          {
            user_id: user.id,
            name: formData.name,
            description: formData.description,
            website_url: formData.website,
            logo_url: formData.logo,
            category: formData.category,
            status: formData.status,
            country: formData.country,
            tags: formData.tags,
            maker: user.email?.split('@')[0] || 'Utilisateur'
          },
        ]);

      if (error) {
        throw error;
      }

      setFormData({
        name: '',
        description: '',
        website: '',
        logo: '',
        category: '',
        status: 'MVP',
        country: '',
        tags: []
      });
      setCurrentTag('');

      toast({
        title: "Produit soumis",
        description: "Votre produit a été soumis avec succès!",
      });
    } catch (error: any) {
      console.error("Error submitting product:", error);
      toast({
        variant: "destructive",
        title: "Erreur de soumission",
        description: error.message,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const addTag = () => {
    if (currentTag.trim() && !formData.tags.includes(currentTag.trim())) {
      setFormData({ ...formData, tags: [...formData.tags, currentTag.trim()] });
      setCurrentTag('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    setFormData({ ...formData, tags: formData.tags.filter(tag => tag !== tagToRemove) });
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  return (
    <section id="submit" className="py-16 bg-card/30">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold bg-gradient-sunset bg-clip-text text-transparent mb-4">
            {t('submit.title')}
          </h2>
          <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
            {t('submit.description')}
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-6xl mx-auto">
          {/* Formulaire de soumission */}
          <Card className="shadow-elegant">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Rocket className="w-5 h-5" />
                <span>Soumettre votre produit</span>
              </CardTitle>
              <CardDescription>
                Partagez votre innovation avec la communauté tech africaine
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">{t('submit.name')}</Label>
                  <Input
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    placeholder={t('submit.namePlaceholder')}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">{t('submit.description')}</Label>
                  <Textarea
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    placeholder={t('submit.descriptionPlaceholder')}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="website">{t('submit.website')}</Label>
                  <Input
                    id="website"
                    name="website"
                    type="url"
                    value={formData.website}
                    onChange={handleInputChange}
                    placeholder={t('submit.websitePlaceholder')}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="logo">{t('submit.logo')}</Label>
                  <Input
                    id="logo"
                    name="logo"
                    type="url"
                    value={formData.logo}
                    onChange={handleInputChange}
                    placeholder={t('submit.logoPlaceholder')}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="category">{t('submit.category')}</Label>
                  <Select onValueChange={(value) => setFormData({ ...formData, category: value })}>
                    <SelectTrigger>
                      <SelectValue placeholder={t('submit.categoryPlaceholder')} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="AI">Intelligence Artificielle</SelectItem>
                      <SelectItem value="Web3">Web3</SelectItem>
                      <SelectItem value="Fintech">Fintech</SelectItem>
                      <SelectItem value="Edtech">Edtech</SelectItem>
                      <SelectItem value="Healthtech">Healthtech</SelectItem>
                      <SelectItem value="E-commerce">E-commerce</SelectItem>
                      <SelectItem value="Social Media">Social Media</SelectItem>
                      <SelectItem value="Other">Autre</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="status">{t('submit.status')}</Label>
                  <Select onValueChange={(value) => setFormData({ ...formData, status: value as 'MVP' | 'Beta' | 'Lancement' })}>
                    <SelectTrigger>
                      <SelectValue placeholder={t('submit.statusPlaceholder')} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="MVP">MVP</SelectItem>
                      <SelectItem value="Beta">Beta</SelectItem>
                      <SelectItem value="Lancement">Lancement</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="country">{t('submit.country')}</Label>
                  <Input
                    id="country"
                    name="country"
                    value={formData.country}
                    onChange={handleInputChange}
                    placeholder={t('submit.countryPlaceholder')}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label>{t('submit.tags')}</Label>
                  <div className="flex space-x-2">
                    <Input
                      type="text"
                      placeholder={t('submit.tagsPlaceholder')}
                      value={currentTag}
                      onChange={(e) => setCurrentTag(e.target.value)}
                    />
                    <Button type="button" size="sm" onClick={addTag}>
                      <Plus className="w-4 h-4 mr-2" />
                      {t('submit.addTag')}
                    </Button>
                  </div>
                  <div className="flex flex-wrap gap-2 mt-2">
                    {formData.tags.map((tag, index) => (
                      <Badge key={index} variant="secondary" className="gap-x-2">
                        {tag}
                        <Button type="button" variant="ghost" size="icon" onClick={() => removeTag(tag)}>
                          <X className="w-4 h-4" />
                        </Button>
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>
              
              <Button 
                type="submit" 
                onClick={handleSubmit}
                disabled={isSubmitting || !user}
                className="w-full"
              >
                {isSubmitting ? "Soumission..." : user ? t('submit.submit') : "Connectez-vous pour soumettre"}
              </Button>
            </CardContent>
          </Card>

          {/* Widget Generator */}
          <Card className="shadow-elegant">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Code className="w-5 h-5" />
                <span>Widget "Featured on AYOHUB"</span>
              </CardTitle>
              <CardDescription>
                Créez un widget pour montrer que vous êtes sur AYOHUB
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="text-center">
                <div className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-4 py-3 rounded-lg inline-flex items-center space-x-3 font-medium mb-4">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 2L2 7v10c0 5.55 3.84 9.74 9 11 5.16-1.26 9-5.45 9-11V7l-10-5z"/>
                  </svg>
                  <span>Présenté sur AYOHUB</span>
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="m9 18 6-6-6-6"/>
                  </svg>
                </div>
                <p className="text-sm text-muted-foreground mb-6">
                  Ajoutez ce widget à votre site pour montrer votre présence sur AYOHUB
                </p>
              </div>

              <FeaturedWidget />

              <Separator />

              <div className="bg-muted p-4 rounded-lg">
                <h4 className="font-medium mb-2">Avantages du widget</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>✨ Augmente votre crédibilité</li>
                  <li>🔗 Génère du trafic vers AYOHUB</li>
                  <li>🎨 Entièrement personnalisable</li>
                  <li>📱 Responsive et accessible</li>
                  <li>⚡ Code optimisé et léger</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
};

export default ProductSubmissionForm;
