-- CONFIGURATION ADMIN POUR CEDRIC
-- Email: <EMAIL>

-- Étape 1: A<PERSON>ter la colonne admin_role si elle n'existe pas
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS admin_role BOOLEAN DEFAULT FALSE;

-- Étape 2: Activer RLS sur les tables principales
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;

-- Étape 3: Créer les politiques admin
DROP POLICY IF EXISTS "Admins can view all profiles" ON profiles;
CREATE POLICY "Ad<PERSON> can view all profiles" ON profiles
FOR SELECT USING (
  auth.uid() IN (SELECT id FROM profiles WHERE admin_role = true)
);

DROP POLICY IF EXISTS "Ad<PERSON> can update all profiles" ON profiles;
CREATE POLICY "Admins can update all profiles" ON profiles
FOR UPDATE USING (
  auth.uid() IN (SELECT id FROM profiles WHERE admin_role = true)
);

DROP POLICY IF EXISTS "Admins can manage all products" ON products;
CREATE POLICY "Ad<PERSON> can manage all products" ON products
FOR ALL USING (
  auth.uid() IN (SELECT id FROM profiles WHERE admin_role = true)
);

-- Étape 4: Fonction pour vérifier si l'utilisateur est admin
CREATE OR REPLACE FUNCTION is_admin()
RETURNS BOOLEAN
LANGUAGE sql
SECURITY DEFINER
STABLE
AS $$
  SELECT EXISTS(
    SELECT 1 FROM profiles 
    WHERE id = auth.uid() AND admin_role = true
  );
$$;

-- Étape 5: S'assurer que admin_role est false par défaut pour tous
UPDATE profiles
SET
  admin_role = false,
  role = COALESCE(role, 'supporter')
WHERE admin_role IS NULL;

-- Étape 6: Vérification - Tous les utilisateurs ont admin_role = false par défaut
SELECT
  '✅ Configuration terminée - Tous les utilisateurs sont des utilisateurs normaux par défaut' as resultat;

-- Étape 7: Afficher tous les utilisateurs et leur statut
SELECT
  email,
  admin_role,
  role,
  created_at
FROM profiles
ORDER BY created_at DESC
LIMIT 10;

-- Message final
SELECT '🎉 Configuration de base terminée!' as message;
SELECT 'Pour activer un admin, utilisez: UPDATE profiles SET admin_role = true WHERE email = ''<EMAIL>'';' as instruction;
