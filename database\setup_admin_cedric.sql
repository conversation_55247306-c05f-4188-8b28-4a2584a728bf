-- CONFIGURATION ADMIN POUR CEDRIC
-- Email: <EMAIL>

-- Étape 1: A<PERSON>ter la colonne admin_role si elle n'existe pas
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS admin_role BOOLEAN DEFAULT FALSE;

-- Étape 2: Activer RLS sur les tables principales
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;

-- Étape 3: Créer les politiques admin
DROP POLICY IF EXISTS "Admins can view all profiles" ON profiles;
CREATE POLICY "Ad<PERSON> can view all profiles" ON profiles
FOR SELECT USING (
  auth.uid() IN (SELECT id FROM profiles WHERE admin_role = true)
);

DROP POLICY IF EXISTS "Ad<PERSON> can update all profiles" ON profiles;
CREATE POLICY "Admins can update all profiles" ON profiles
FOR UPDATE USING (
  auth.uid() IN (SELECT id FROM profiles WHERE admin_role = true)
);

DROP POLICY IF EXISTS "Admins can manage all products" ON products;
CREATE POLICY "Ad<PERSON> can manage all products" ON products
FOR ALL USING (
  auth.uid() IN (SELECT id FROM profiles WHERE admin_role = true)
);

-- Étape 4: Fonction pour vérifier si l'utilisateur est admin
CREATE OR REPLACE FUNCTION is_admin()
RETURNS BOOLEAN
LANGUAGE sql
SECURITY DEFINER
STABLE
AS $$
  SELECT EXISTS(
    SELECT 1 FROM profiles 
    WHERE id = auth.uid() AND admin_role = true
  );
$$;

-- Étape 5: Activer les privilèges admin pour Cedric
UPDATE profiles 
SET 
  admin_role = true, 
  role = 'admin',
  bio = COALESCE(bio, 'Administrateur AYOHUB'),
  updated_at = NOW()
WHERE email = '<EMAIL>';

-- Étape 6: Vérification
SELECT 
  CASE 
    WHEN EXISTS(SELECT 1 FROM profiles WHERE email = '<EMAIL>' AND admin_role = true)
    THEN '✅ SUCCÈS: Cedric est maintenant administrateur!'
    ELSE '❌ ERREUR: Compte non trouvé. Créez d''abord un <NAME_EMAIL>'
  END as resultat;

-- Étape 7: Afficher les informations admin
SELECT 
  email,
  admin_role,
  role,
  bio,
  created_at
FROM profiles 
WHERE email = '<EMAIL>';

-- Message final
SELECT '🎉 Configuration terminée pour Cedric!' as message;
SELECT 'Connectez-<NAME_EMAIL> pour accéder au tableau de bord admin' as instruction;
