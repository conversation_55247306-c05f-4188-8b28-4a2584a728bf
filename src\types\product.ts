export interface Product {
  id: string;
  name: string;
  description: string;
  logo: string;
  website: string;
  country: string;
  category: string;
  status: 'MVP' | 'Beta' | 'Lancement';
  votes: number;
  createdAt: string;
  maker: string;
  tags: string[];
  images?: string[];
  hasVoted?: boolean;
}

export interface ProductSubmission {
  name: string;
  description: string;
  logo: string;
  website: string;
  country: string;
  category: string;
  status: 'MVP' | 'Beta' | 'Lancement';
  maker: string;
  tags: string[];
}