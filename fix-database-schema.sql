-- Fix Database Schema Issues for Africa Tech Spotlight
-- Run this script in Supabase SQL Editor to resolve query errors

-- 1. Check if maker column exists, if not add it
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'products' AND column_name = 'maker') THEN
        ALTER TABLE public.products ADD COLUMN maker TEXT DEFAULT 'Utilisateur';
        
        -- Update existing products with maker information
        UPDATE public.products 
        SET maker = COALESCE(
            (SELECT full_name FROM public.profiles WHERE id = products.user_id),
            (SELECT split_part(email, '@', 1) FROM public.profiles WHERE id = products.user_id),
            'Utilisateur'
        )
        WHERE maker IS NULL OR maker = 'Utilisateur';
        
        RAISE NOTICE 'Added maker column to products table';
    ELSE
        RAISE NOTICE 'Maker column already exists in products table';
    END IF;
END $$;

-- 2. Verify all required tables exist
DO $$
BEGIN
    -- Check products table
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'products') THEN
        RAISE EXCEPTION 'Products table does not exist. Please run migrations first.';
    END IF;
    
    -- Check profiles table
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'profiles') THEN
        RAISE EXCEPTION 'Profiles table does not exist. Please run migrations first.';
    END IF;
    
    -- Check product_comments table
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'product_comments') THEN
        RAISE EXCEPTION 'Product_comments table does not exist. Please run migrations first.';
    END IF;
    
    -- Check votes table
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'votes') THEN
        RAISE EXCEPTION 'Votes table does not exist. Please run migrations first.';
    END IF;
    
    RAISE NOTICE 'All required tables exist';
END $$;

-- 3. Check and fix RLS policies
-- Ensure products can be read by everyone
DROP POLICY IF EXISTS "Anyone can view products" ON public.products;
CREATE POLICY "Anyone can view products" ON public.products FOR SELECT USING (true);

-- Ensure profiles can be read by everyone (needed for joins)
DROP POLICY IF EXISTS "Users can view all profiles" ON public.profiles;
CREATE POLICY "Users can view all profiles" ON public.profiles FOR SELECT USING (true);

-- Ensure comments can be read by everyone
DROP POLICY IF EXISTS "Anyone can view comments" ON public.product_comments;
CREATE POLICY "Anyone can view comments" ON public.product_comments FOR SELECT USING (true);

-- 4. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_products_created_at ON public.products(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_products_votes ON public.products(votes DESC);
CREATE INDEX IF NOT EXISTS idx_products_user_id ON public.products(user_id);
CREATE INDEX IF NOT EXISTS idx_product_comments_product_id ON public.product_comments(product_id);
CREATE INDEX IF NOT EXISTS idx_product_comments_user_id ON public.product_comments(user_id);
CREATE INDEX IF NOT EXISTS idx_votes_product_id ON public.votes(product_id);
CREATE INDEX IF NOT EXISTS idx_votes_user_id ON public.votes(user_id);

-- 5. Verify data integrity
SELECT 
    'products' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN maker IS NOT NULL THEN 1 END) as records_with_maker
FROM public.products
UNION ALL
SELECT 
    'profiles' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN full_name IS NOT NULL THEN 1 END) as records_with_name
FROM public.profiles
UNION ALL
SELECT 
    'product_comments' as table_name,
    COUNT(*) as total_records,
    0 as extra_info
FROM public.product_comments
UNION ALL
SELECT 
    'votes' as table_name,
    COUNT(*) as total_records,
    0 as extra_info
FROM public.votes;

-- 6. Test basic queries that were failing
-- Test products query
SELECT 
    id, name, description, logo_url, website_url, country, 
    category, status, votes, tags, created_at, user_id, maker
FROM public.products 
ORDER BY created_at DESC 
LIMIT 5;

-- Test product with profile join
SELECT 
    p.id, p.name, p.maker,
    pr.full_name, pr.email
FROM public.products p
LEFT JOIN public.profiles pr ON p.user_id = pr.id
LIMIT 5;

-- Test comments query
SELECT 
    c.id, c.content, c.created_at,
    pr.full_name, pr.email
FROM public.product_comments c
LEFT JOIN public.profiles pr ON c.user_id = pr.id
LIMIT 5;

RAISE NOTICE 'Database schema fixes completed successfully!';
