import { User } from "@/types/user";

interface Vote {
  productId: string;
  userId: string;
  timestamp: string;
  ipAddress?: string;
  userAgent?: string;
}

interface VoteHistory {
  [productId: string]: Vote[];
}

class VoteService {
  private static instance: VoteService;
  private voteHistory: VoteHistory = {};
  private dailyVoteLimits: { [userId: string]: { date: string; count: number } } = {};
  private readonly MAX_DAILY_VOTES = 10;
  private readonly VOTE_COOLDOWN = 30000; // 30 secondes entre votes

  private constructor() {
    this.loadVoteHistory();
    this.loadDailyLimits();
  }

  static getInstance(): VoteService {
    if (!VoteService.instance) {
      VoteService.instance = new VoteService();
    }
    return VoteService.instance;
  }

  private loadVoteHistory(): void {
    const saved = localStorage.getItem('ayohub_vote_history');
    if (saved) {
      this.voteHistory = JSON.parse(saved);
    }
  }

  private loadDailyLimits(): void {
    const saved = localStorage.getItem('ayohub_daily_limits');
    if (saved) {
      this.dailyVoteLimits = JSON.parse(saved);
    }
  }

  private saveVoteHistory(): void {
    localStorage.setItem('ayohub_vote_history', JSON.stringify(this.voteHistory));
  }

  private saveDailyLimits(): void {
    localStorage.setItem('ayohub_daily_limits', JSON.stringify(this.dailyVoteLimits));
  }

  private getToday(): string {
    return new Date().toISOString().split('T')[0];
  }

  private updateDailyLimit(userId: string): void {
    const today = this.getToday();
    if (!this.dailyVoteLimits[userId] || this.dailyVoteLimits[userId].date !== today) {
      this.dailyVoteLimits[userId] = { date: today, count: 1 };
    } else {
      this.dailyVoteLimits[userId].count++;
    }
    this.saveDailyLimits();
  }

  private checkDailyLimit(userId: string): boolean {
    const today = this.getToday();
    const userLimit = this.dailyVoteLimits[userId];
    return !userLimit || userLimit.date !== today || userLimit.count < this.MAX_DAILY_VOTES;
  }

  private checkCooldown(userId: string): boolean {
    const now = Date.now();
    for (const productVotes of Object.values(this.voteHistory)) {
      const userVote = productVotes.find(vote => vote.userId === userId);
      if (userVote) {
        const lastVoteTime = new Date(userVote.timestamp).getTime();
        if (now - lastVoteTime < this.VOTE_COOLDOWN) {
          return false;
        }
      }
    }
    return true;
  }

  canVote(productId: string, user: User): { canVote: boolean; reason?: string } {
    // Vérifier si l'utilisateur est connecté
    if (!user) {
      return { canVote: false, reason: "Vous devez être connecté pour voter" };
    }

    // Vérifier si l'utilisateur a déjà voté pour ce produit
    const productVotes = this.voteHistory[productId] || [];
    const hasVoted = productVotes.some(vote => vote.userId === user.id);
    if (hasVoted) {
      return { canVote: false, reason: "Vous avez déjà voté pour ce produit" };
    }

    // Vérifier la limite quotidienne
    if (!this.checkDailyLimit(user.id)) {
      return { canVote: false, reason: `Limite de ${this.MAX_DAILY_VOTES} votes par jour atteinte` };
    }

    // Vérifier le cooldown
    if (!this.checkCooldown(user.id)) {
      return { canVote: false, reason: "Veuillez attendre 30 secondes entre les votes" };
    }

    // Vérifier l'âge du compte (minimum 24h)
    const accountAge = Date.now() - new Date(user.joinedAt).getTime();
    const minAge = 24 * 60 * 60 * 1000; // 24 heures
    if (accountAge < minAge) {
      return { canVote: false, reason: "Votre compte doit avoir au moins 24h pour voter" };
    }

    return { canVote: true };
  }

  async vote(productId: string, user: User): Promise<{ success: boolean; message: string }> {
    const { canVote, reason } = this.canVote(productId, user);
    
    if (!canVote) {
      return { success: false, message: reason || "Vote non autorisé" };
    }

    // Enregistrer le vote
    const vote: Vote = {
      productId,
      userId: user.id,
      timestamp: new Date().toISOString(),
      ipAddress: "simulated_ip", // En vrai, récupéré côté serveur
      userAgent: navigator.userAgent
    };

    if (!this.voteHistory[productId]) {
      this.voteHistory[productId] = [];
    }
    
    this.voteHistory[productId].push(vote);
    this.updateDailyLimit(user.id);
    this.saveVoteHistory();

    // Mettre à jour les votes de l'utilisateur
    const updatedUser = { ...user };
    if (!updatedUser.productsUpvoted.includes(productId)) {
      updatedUser.productsUpvoted.push(productId);
      localStorage.setItem('ayohub_user', JSON.stringify(updatedUser));
    }

    return { success: true, message: "Vote enregistré avec succès" };
  }

  hasUserVoted(productId: string, userId: string): boolean {
    const productVotes = this.voteHistory[productId] || [];
    return productVotes.some(vote => vote.userId === userId);
  }

  getVoteCount(productId: string): number {
    return (this.voteHistory[productId] || []).length;
  }

  getVoteCredibility(productId: string): {
    totalVotes: number;
    uniqueVoters: number;
    credibilityScore: number;
    averageAccountAge: number;
  } {
    const votes = this.voteHistory[productId] || [];
    const uniqueVoters = new Set(votes.map(vote => vote.userId)).size;
    
    // Score de crédibilité basé sur le ratio votes uniques / total votes
    const credibilityScore = votes.length > 0 ? (uniqueVoters / votes.length) * 100 : 0;
    
    // Âge moyen des comptes (simulation)
    const averageAccountAge = votes.length > 0 ? 
      votes.reduce((sum, vote) => sum + Math.random() * 365, 0) / votes.length : 0;

    return {
      totalVotes: votes.length,
      uniqueVoters,
      credibilityScore: Math.round(credibilityScore),
      averageAccountAge: Math.round(averageAccountAge)
    };
  }

  getUserDailyVoteCount(userId: string): number {
    const today = this.getToday();
    const userLimit = this.dailyVoteLimits[userId];
    return userLimit && userLimit.date === today ? userLimit.count : 0;
  }

  getRemainingDailyVotes(userId: string): number {
    return this.MAX_DAILY_VOTES - this.getUserDailyVoteCount(userId);
  }
}

export default VoteService;