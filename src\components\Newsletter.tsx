import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";

const Newsletter = () => {
  const [email, setEmail] = useState("");
  const [isSubscribing, setIsSubscribing] = useState(false);
  const { toast } = useToast();

  const handleSubscribe = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email) {
      toast({
        title: "Erreur",
        description: "Veuillez entrer votre adresse email.",
        variant: "destructive",
      });
      return;
    }

    setIsSubscribing(true);
    
    // Simulate subscription
    try {
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      toast({
        title: "Inscription réussie ! 🎉",
        description: "Vous recevrez notre newsletter chaque semaine.",
      });
      
      setEmail("");
    } catch (error) {
      toast({
        title: "Erreur",
        description: "Une erreur est survenue lors de l'inscription.",
        variant: "destructive",
      });
    } finally {
      setIsSubscribing(false);
    }
  };

  return (
    <section className="py-16 bg-gradient-glow">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          {/* Main Newsletter Card */}
          <Card className="shadow-elegant border-border/50 mb-8">
            <CardContent className="pt-8">
              <div className="text-center space-y-6">
                <div>
                  <h2 className="text-3xl md:text-4xl font-bold mb-4">
                    <span className="bg-gradient-sunset bg-clip-text text-transparent">
                      Newsletter AYOHUB
                    </span>
                  </h2>
                  <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
                    Recevez chaque semaine le meilleur de l'innovation tech africaine : 
                    nouveaux produits, success stories et tendances du continent.
                  </p>
                </div>

                <form onSubmit={handleSubscribe} className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
                  <Input
                    type="email"
                    placeholder="<EMAIL>"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="flex-1"
                  />
                  <Button 
                    type="submit" 
                    disabled={isSubscribing}
                    className="min-w-[140px]"
                  >
                    {isSubscribing ? "Inscription..." : "S'abonner"}
                  </Button>
                </form>

                <p className="text-xs text-muted-foreground">
                  Pas de spam, désabonnement en un clic. 
                  Rejoignez les <strong>2,500+</strong> abonnés.
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Newsletter Preview */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card className="border-border/50">
              <CardHeader className="pb-3">
                <CardTitle className="text-lg">🚀 Top Produits</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Les 5 produits les plus votés de la semaine avec analyses détaillées.
                </p>
              </CardContent>
            </Card>

            <Card className="border-border/50">
              <CardHeader className="pb-3">
                <CardTitle className="text-lg">👨‍💻 Nouveaux Makers</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Portraits des entrepreneurs qui révolutionnent la tech africaine.
                </p>
              </CardContent>
            </Card>

            <Card className="border-border/50">
              <CardHeader className="pb-3">
                <CardTitle className="text-lg">📊 Insights & Tendances</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Analyses des secteurs émergents et opportunités d'investissement.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Newsletter;