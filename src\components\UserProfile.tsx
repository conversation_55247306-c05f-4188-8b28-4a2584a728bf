import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { User } from "@/types/user";

interface UserProfileProps {
  user: User;
  isOwnProfile?: boolean;
}

const UserProfile = ({ user, isOwnProfile = false }: UserProfileProps) => {
  const getRoleColor = (role: string) => {
    switch (role) {
      case 'Maker':
        return 'bg-primary/20 text-primary border-primary/30';
      case 'Investisseur':
        return 'bg-green-500/20 text-green-700 border-green-500/30';
      case 'Supporter':
        return 'bg-accent/20 text-accent border-accent/30';
      default:
        return 'bg-muted text-muted-foreground';
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Profile Header */}
      <Card className="shadow-elegant">
        <CardContent className="pt-6">
          <div className="flex flex-col md:flex-row items-start md:items-center space-y-4 md:space-y-0 md:space-x-6">
            <Avatar className="w-24 h-24">
              <AvatarImage src={user.avatar} />
              <AvatarFallback className="text-2xl">{user.name[0]}</AvatarFallback>
            </Avatar>
            
            <div className="flex-1 space-y-3">
              <div>
                <h1 className="text-2xl font-bold">{user.name}</h1>
                <p className="text-muted-foreground">{user.email}</p>
              </div>
              
              <div className="flex flex-wrap gap-2">
                <Badge className={getRoleColor(user.role)}>
                  {user.role}
                </Badge>
                <Badge variant="outline">
                  📍 {user.country}
                </Badge>
                <Badge variant="outline">
                  Membre depuis {new Date(user.joinedAt).getFullYear()}
                </Badge>
              </div>
              
              <p className="text-foreground max-w-2xl">{user.bio}</p>
              
              <div className="flex items-center space-x-6 text-sm">
                <span><strong>{user.followers}</strong> followers</span>
                <span><strong>{user.following}</strong> suivis</span>
                <span><strong>{user.productsLaunched.length}</strong> produits lancés</span>
              </div>
            </div>
            
            <div className="flex flex-col space-y-2">
              {isOwnProfile ? (
                <Button variant="outline">
                  Modifier le profil
                </Button>
              ) : (
                <>
                  <Button>Suivre</Button>
                  <Button variant="outline">Message</Button>
                </>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Produits lancés
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-primary">
              {user.productsLaunched.length}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Votes donnés
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-accent">
              {user.productsUpvoted.length}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Impact communauté
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {user.followers + user.productsLaunched.length * 10}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle>Activité récente</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center space-x-3 text-sm">
              <div className="w-2 h-2 bg-primary rounded-full"></div>
              <span>A voté pour <strong>MoMo Pay</strong></span>
              <span className="text-muted-foreground">Il y a 2h</span>
            </div>
            <div className="flex items-center space-x-3 text-sm">
              <div className="w-2 h-2 bg-accent rounded-full"></div>
              <span>A commenté sur <strong>EduLearn Africa</strong></span>
              <span className="text-muted-foreground">Il y a 1j</span>
            </div>
            <div className="flex items-center space-x-3 text-sm">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span>A lancé un nouveau produit</span>
              <span className="text-muted-foreground">Il y a 3j</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default UserProfile;