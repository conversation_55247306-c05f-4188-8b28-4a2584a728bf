import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useLanguage } from "@/contexts/LanguageContext";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { User } from "@/types/user";

interface UserProfileProps {
  user: User;
  isOwnProfile?: boolean;
}

interface Activity {
  id: string;
  type: 'vote' | 'comment' | 'product' | 'join';
  description: string;
  timestamp: string;
  productName?: string;
}

const UserProfile = ({ user, isOwnProfile = false }: UserProfileProps) => {
  const { t } = useLanguage();
  const { toast } = useToast();
  const [activities, setActivities] = useState<Activity[]>([]);
  const [stats, setStats] = useState({
    productsLaunched: 0,
    votesGiven: 0,
    communityImpact: 0
  });
  const [isFollowing, setIsFollowing] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadUserStats();
    loadUserActivity();
  }, [user.id]);

  const loadUserStats = async () => {
    try {
      // Load products launched by user
      const { data: products } = await supabase
        .from('products')
        .select('id')
        .eq('user_id', user.id);

      // Load votes given by user
      const { data: votes } = await supabase
        .from('votes')
        .select('id')
        .eq('user_id', user.id);

      // Load comments by user
      const { data: comments } = await supabase
        .from('product_comments')
        .select('id')
        .eq('user_id', user.id);

      setStats({
        productsLaunched: products?.length || 0,
        votesGiven: votes?.length || 0,
        communityImpact: (products?.length || 0) * 10 + (votes?.length || 0) + (comments?.length || 0) * 2
      });
    } catch (error) {
      console.error('Error loading user stats:', error);
    }
  };

  const loadUserActivity = async () => {
    try {
      const activities: Activity[] = [];

      // Load recent votes with product names
      const { data: recentVotes } = await supabase
        .from('votes')
        .select(`
          created_at,
          product_id
        `)
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(5);

      if (recentVotes) {
        for (const vote of recentVotes) {
          const { data: product } = await supabase
            .from('products')
            .select('name')
            .eq('id', vote.product_id)
            .single();

          if (product) {
            activities.push({
              id: `vote-${vote.created_at}`,
              type: 'vote',
              description: `${t('profile.voted_for')} ${product.name}`,
              timestamp: vote.created_at,
              productName: product.name
            });
          }
        }
      }

      // Sort activities by timestamp
      activities.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
      setActivities(activities.slice(0, 10));
    } catch (error) {
      console.error('Error loading user activity:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleFollow = async () => {
    try {
      if (isFollowing) {
        setIsFollowing(false);
        toast({
          title: "Succès",
          description: `Vous ne suivez plus ${user.name}`
        });
      } else {
        setIsFollowing(true);
        toast({
          title: "Succès",
          description: `Vous suivez maintenant ${user.name}`
        });
      }
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Erreur",
        description: "Impossible de modifier le suivi"
      });
    }
  };

  const handleEditProfile = () => {
    toast({
      title: "Fonctionnalité à venir",
      description: "L'édition de profil sera bientôt disponible"
    });
  };

  const handleMessage = () => {
    toast({
      title: "Fonctionnalité à venir",
      description: "La messagerie sera bientôt disponible"
    });
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'Maker':
        return 'bg-primary/20 text-primary border-primary/30';
      case 'Investisseur':
        return 'bg-green-500/20 text-green-700 border-green-500/30';
      case 'Supporter':
        return 'bg-accent/20 text-accent border-accent/30';
      default:
        return 'bg-muted text-muted-foreground';
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Profile Header */}
      <Card className="shadow-elegant">
        <CardContent className="pt-6">
          <div className="flex flex-col md:flex-row items-start md:items-center space-y-4 md:space-y-0 md:space-x-6">
            <Avatar className="w-24 h-24">
              <AvatarImage src={user.avatar} />
              <AvatarFallback className="text-2xl">{user.name[0]}</AvatarFallback>
            </Avatar>
            
            <div className="flex-1 space-y-3">
              <div>
                <h1 className="text-2xl font-bold">{user.name}</h1>
                <p className="text-muted-foreground">{user.email}</p>
              </div>
              
              <div className="flex flex-wrap gap-2">
                <Badge className={getRoleColor(user.role)}>
                  {user.role}
                </Badge>
                <Badge variant="outline">
                  📍 {user.country}
                </Badge>
                <Badge variant="outline">
                  {t('profile.member_since')} {new Date(user.joinedAt).getFullYear()}
                </Badge>
              </div>
              
              <p className="text-foreground max-w-2xl">{user.bio}</p>
              
              <div className="flex items-center space-x-6 text-sm">
                <span><strong>{user.followers}</strong> {t('profile.followers')}</span>
                <span><strong>{user.following}</strong> {t('profile.following')}</span>
                <span><strong>{stats.productsLaunched}</strong> {t('profile.products_launched')}</span>
              </div>
            </div>
            
            <div className="flex flex-col space-y-2">
              {isOwnProfile ? (
                <Button variant="outline" onClick={handleEditProfile}>
                  {t('profile.edit')}
                </Button>
              ) : (
                <>
                  <Button onClick={handleFollow}>
                    {isFollowing ? t('profile.unfollow') : t('profile.follow')}
                  </Button>
                  <Button variant="outline" onClick={handleMessage}>
                    {t('profile.message')}
                  </Button>
                </>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              {t('profile.products_launched')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-primary">
              {stats.productsLaunched}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              {t('profile.votes_given')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-accent">
              {stats.votesGiven}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              {t('profile.community_impact')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {stats.communityImpact}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle>{t('profile.recent_activity')}</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="w-6 h-6 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
            </div>
          ) : activities.length > 0 ? (
            <div className="space-y-4">
              {activities.map((activity) => (
                <div key={activity.id} className="flex items-center space-x-3 text-sm">
                  <div className={`w-2 h-2 rounded-full ${
                    activity.type === 'vote' ? 'bg-primary' :
                    activity.type === 'comment' ? 'bg-accent' :
                    activity.type === 'product' ? 'bg-green-500' :
                    'bg-blue-500'
                  }`}></div>
                  <span>{activity.description}</span>
                  <span className="text-muted-foreground">
                    {new Date(activity.timestamp).toLocaleDateString()}
                  </span>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              {t('profile.no_activity')}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default UserProfile;