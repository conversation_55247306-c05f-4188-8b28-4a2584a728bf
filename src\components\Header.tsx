
import { useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/contexts/AuthContext";
import { useLanguage } from "@/contexts/LanguageContext";
import LanguageSelector from "./LanguageSelector";
import <PERSON>yohub<PERSON><PERSON> from "./AyohubLogo";
import { useAdmin } from "@/hooks/useAdmin";
import { Menu, X, Shield, Settings } from "lucide-react";

const Header = () => {
  const { user, signOut } = useAuth();
  const { t } = useLanguage();
  const { isAdmin } = useAdmin();
  const navigate = useNavigate();
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const handleSignOut = async () => {
    await signOut();
    navigate('/');
  };

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    } else {
      // If not on home page, navigate to home first then scroll
      navigate('/');
      setTimeout(() => {
        const element = document.getElementById(sectionId);
        if (element) {
          element.scrollIntoView({ behavior: 'smooth' });
        }
      }, 100);
    }
    setIsMenuOpen(false);
  };

  return (
    <header className="bg-card/90 backdrop-blur-sm border-b border-border/50 sticky top-0 z-50">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          <Link to="/" className="flex items-center">
            <AyohubLogo size="md" />
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <Link to="/" className="text-foreground hover:text-primary transition-colors">
              {t('nav.home')}
            </Link>
            <Link to="/about" className="text-foreground hover:text-primary transition-colors">
              {t('nav.about')}
            </Link>
            <button
              onClick={() => scrollToSection('products')}
              className="text-foreground hover:text-primary transition-colors"
            >
              {t('nav.products')}
            </button>
            <button
              onClick={() => scrollToSection('submit')}
              className="text-foreground hover:text-primary transition-colors"
            >
              {t('hero.submit')}
            </button>
          </nav>

          {/* User Menu */}
          <div className="hidden md:flex items-center space-x-4">
            <LanguageSelector />
            
            {user ? (
              <div className="flex items-center space-x-3">
                {isAdmin && (
                  <Link to="/admin">
                    <Button variant="outline" size="sm" className="text-xs">
                      <Shield className="w-4 h-4 mr-1" />
                      Admin
                    </Button>
                  </Link>
                )}
                <Link to="/dashboard">
                  <Button variant="outline" size="sm">
                    Dashboard
                  </Button>
                </Link>
                <Button onClick={handleSignOut} variant="outline" size="sm">
                  {t('nav.logout')}
                </Button>
              </div>
            ) : (
              <div className="flex items-center space-x-2">
                <Link to="/login">
                  <Button variant="outline" size="sm">
                    {t('nav.login')}
                  </Button>
                </Link>
                <Link to="/auth">
                  <Button size="sm">
                    {t('auth.signup')}
                  </Button>
                </Link>
              </div>
            )}
          </div>

          {/* Mobile menu button */}
          <button
            className="md:hidden"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            {isMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
          </button>
        </div>

        {/* Mobile menu */}
        {isMenuOpen && (
          <div className="md:hidden py-4 border-t border-border/50">
            <nav className="flex flex-col space-y-4">
              <Link to="/" className="text-foreground hover:text-primary transition-colors">
                {t('nav.home')}
              </Link>
              <Link to="/about" className="text-foreground hover:text-primary transition-colors">
                {t('nav.about')}
              </Link>
              <button
                onClick={() => scrollToSection('products')}
                className="text-foreground hover:text-primary transition-colors text-left"
              >
                {t('nav.products')}
              </button>
              <button
                onClick={() => scrollToSection('submit')}
                className="text-foreground hover:text-primary transition-colors text-left"
              >
                {t('hero.submit')}
              </button>
              
              <div className="pt-4 border-t border-border/50">
                <LanguageSelector />
                
                {user ? (
                  <div className="flex flex-col space-y-2 mt-4">
                    {isAdmin && (
                      <Link to="/admin">
                        <Button variant="outline" size="sm" className="w-full justify-start">
                          <Shield className="w-4 h-4 mr-2" />
                          Admin Dashboard
                        </Button>
                      </Link>
                    )}
                    <Link to="/dashboard">
                      <Button variant="outline" size="sm" className="w-full">
                        {t('nav.dashboard')}
                      </Button>
                    </Link>
                    <Button onClick={handleSignOut} variant="outline" size="sm" className="w-full">
                      {t('nav.logout')}
                    </Button>
                  </div>
                ) : (
                  <div className="flex flex-col space-y-2 mt-4">
                    <Link to="/login">
                      <Button variant="outline" size="sm" className="w-full">
                        {t('nav.login')}
                      </Button>
                    </Link>
                    <Link to="/auth">
                      <Button size="sm" className="w-full">
                        {t('auth.signup')}
                      </Button>
                    </Link>
                  </div>
                )}
              </div>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;
