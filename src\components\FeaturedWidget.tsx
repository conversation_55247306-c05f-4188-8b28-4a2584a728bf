
import { useState, useEffect } from "react";
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Copy, ExternalLink, Code, Palette, Download, Heart, TrendingUp } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useLanguage } from "@/contexts/LanguageContext";
import { supabase } from "@/integrations/supabase/client";
import AyohubLogo from "./AyohubLogo";

interface WidgetConfig {
  theme: 'light' | 'dark' | 'gradient' | 'modern' | 'minimal';
  size: 'small' | 'medium' | 'large';
  showLogo: boolean;
  showVotes: boolean;
  customText: string;
  productName: string;
  productUrl: string;
  productId: string;
}

const FeaturedWidget = () => {
  const { toast } = useToast();
  const { t } = useLanguage();
  const [config, setConfig] = useState<WidgetConfig>({
    theme: 'modern',
    size: 'medium',
    showLogo: true,
    showVotes: true,
    customText: '',
    productName: '',
    productUrl: '',
    productId: ''
  });
  const [generatedCode, setGeneratedCode] = useState('');
  const [voteCount, setVoteCount] = useState(0);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    generateCode();
  }, [config, voteCount]);

  useEffect(() => {
    if (config.productId) {
      fetchVoteCount();
    }
  }, [config.productId]);

  const fetchVoteCount = async () => {
    if (!config.productId) return;

    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('products')
        .select('votes')
        .eq('id', config.productId)
        .single();

      if (error) throw error;
      setVoteCount(data?.votes || 0);
    } catch (error) {
      console.error('Error fetching vote count:', error);
      setVoteCount(0);
    } finally {
      setLoading(false);
    }
  };

  const generateCode = () => {
    const { theme, size, showLogo, showVotes, customText, productName, productUrl } = config;

    const themeClasses = {
      light: 'bg-white border border-gray-200 text-gray-900 shadow-sm',
      dark: 'bg-gray-900 border border-gray-700 text-white shadow-lg',
      gradient: 'bg-gradient-to-r from-purple-500 to-pink-500 text-white border-0 shadow-lg',
      modern: 'bg-gradient-to-r from-blue-600 to-purple-600 text-white border-0 shadow-xl',
      minimal: 'bg-gray-50 border border-gray-300 text-gray-800 shadow-sm'
    };

    const sizeClasses = {
      small: 'px-3 py-2 text-sm gap-2',
      medium: 'px-4 py-3 text-base gap-3',
      large: 'px-6 py-4 text-lg gap-4'
    };

    const displayText = customText || `${productName} ${t('widget.featured_text')}`;
    
    const htmlCode = `
<!-- Enhanced AYOHUB Widget -->
<div class="ayohub-widget ${themeClasses[theme]} ${sizeClasses[size]} rounded-xl inline-flex items-center font-medium transition-all hover:shadow-2xl hover:scale-105 cursor-pointer relative overflow-hidden" onclick="window.open('${productUrl || 'https://ayohub.com'}', '_blank')">
  ${showLogo ? `
  <div class="flex-shrink-0 w-6 h-6 rounded-lg bg-gradient-to-br from-orange-500 via-red-500 to-purple-600 flex items-center justify-center">
    <svg width="16" height="16" viewBox="0 0 24 24" fill="white" xmlns="http://www.w3.org/2000/svg">
      <path d="M12 2C12 2 8 3 6 6C4 9 4 12 6 15C8 18 12 19 12 19C12 19 16 18 18 15C20 12 20 9 18 6C16 3 12 2 12 2Z" opacity="0.8"/>
      <circle cx="9" cy="8" r="1"/>
      <circle cx="15" cy="8" r="1"/>
      <circle cx="12" cy="12" r="1.5"/>
      <path d="M12 4L13 6L12 8L11 6Z" opacity="0.9"/>
    </svg>
  </div>
  ` : ''}
  <span class="flex-1">${displayText}</span>
  ${showVotes && voteCount > 0 ? `
  <div class="flex items-center gap-1 bg-white/20 backdrop-blur-sm rounded-full px-2 py-1 text-xs font-semibold">
    <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
      <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
    </svg>
    <span>${voteCount}</span>
  </div>
  ` : ''}
  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="flex-shrink-0">
    <path d="m9 18 6-6-6-6"/>
  </svg>
</div>

<style>
.ayohub-widget {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  position: relative;
}
.ayohub-widget:hover {
  transform: translateY(-3px) scale(1.02);
  filter: brightness(1.1);
}
.ayohub-widget::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
  border-radius: inherit;
  pointer-events: none;
}
.ayohub-widget:active {
  transform: translateY(-1px) scale(0.98);
}
</style>`.trim();

    setGeneratedCode(htmlCode);
  };

  const copyToClipboard = () => {
    navigator.clipboard.writeText(generatedCode);
    toast({
      title: t('widget.copy_success'),
      description: t('widget.copy_description')
    });
  };

  const downloadCode = () => {
    const blob = new Blob([generatedCode], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'ayohub-widget.html';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const previewWidget = () => {
    const { theme, size, showLogo, showVotes, customText, productName } = config;

    const themeStyles = {
      light: { background: 'white', color: '#1f2937', border: '1px solid #e5e7eb', boxShadow: '0 1px 3px rgba(0,0,0,0.1)' },
      dark: { background: '#111827', color: 'white', border: '1px solid #374151', boxShadow: '0 4px 6px rgba(0,0,0,0.3)' },
      gradient: { background: 'linear-gradient(to right, #8b5cf6, #ec4899)', color: 'white', border: 'none', boxShadow: '0 8px 25px rgba(139,92,246,0.3)' },
      modern: { background: 'linear-gradient(to right, #2563eb, #8b5cf6)', color: 'white', border: 'none', boxShadow: '0 8px 25px rgba(37,99,235,0.3)' },
      minimal: { background: '#f9fafb', color: '#1f2937', border: '1px solid #d1d5db', boxShadow: '0 1px 3px rgba(0,0,0,0.05)' }
    };

    const sizeStyles = {
      small: { padding: '8px 12px', fontSize: '14px', gap: '8px' },
      medium: { padding: '12px 16px', fontSize: '16px', gap: '12px' },
      large: { padding: '16px 24px', fontSize: '18px', gap: '16px' }
    };

    const displayText = customText || `${productName} ${t('widget.featured_text')}`;

    return (
      <div
        style={{
          ...themeStyles[theme],
          ...sizeStyles[size],
          borderRadius: '12px',
          display: 'inline-flex',
          alignItems: 'center',
          fontWeight: '500',
          cursor: 'pointer',
          transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
          fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
          position: 'relative',
          overflow: 'hidden'
        }}
        onMouseEnter={(e) => {
          e.currentTarget.style.transform = 'translateY(-3px) scale(1.02)';
          e.currentTarget.style.filter = 'brightness(1.1)';
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.transform = 'translateY(0) scale(1)';
          e.currentTarget.style.filter = 'brightness(1)';
        }}
      >
        {/* Subtle overlay for enhanced visual effect */}
        <div style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05))',
          pointerEvents: 'none'
        }} />

        {showLogo && (
          <div style={{
            flexShrink: 0,
            zIndex: 1,
            width: '24px',
            height: '24px',
            borderRadius: '8px',
            background: 'linear-gradient(135deg, #f97316, #ef4444, #9333ea)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            <svg width="16" height="16" viewBox="0 0 24 24" fill="white">
              <path d="M12 2C12 2 8 3 6 6C4 9 4 12 6 15C8 18 12 19 12 19C12 19 16 18 18 15C20 12 20 9 18 6C16 3 12 2 12 2Z" opacity="0.8"/>
              <circle cx="9" cy="8" r="1"/>
              <circle cx="15" cy="8" r="1"/>
              <circle cx="12" cy="12" r="1.5"/>
              <path d="M12 4L13 6L12 8L11 6Z" opacity="0.9"/>
            </svg>
          </div>
        )}

        <span style={{ flex: 1, zIndex: 1 }}>{displayText}</span>

        {showVotes && voteCount > 0 && (
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '4px',
            background: 'rgba(255,255,255,0.2)',
            backdropFilter: 'blur(10px)',
            borderRadius: '20px',
            padding: '4px 8px',
            fontSize: '12px',
            fontWeight: '600',
            zIndex: 1
          }}>
            <Heart size={12} fill="currentColor" />
            <span>{voteCount}</span>
          </div>
        )}

        <div style={{ flexShrink: 0, zIndex: 1 }}>
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <path d="m9 18 6-6-6-6"/>
          </svg>
        </div>
      </div>
    );
  };

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline" className="w-full">
          <Code className="w-4 h-4 mr-2" />
          {t('widget.generate')}
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Palette className="w-5 h-5" />
            <span>{t('widget.generator.title')}</span>
          </DialogTitle>
          <DialogDescription>
            {t('widget.generator.description')}
          </DialogDescription>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Configuration */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">{t('widget.config.title')}</h3>

            <div className="space-y-2">
              <Label htmlFor="productName">{t('widget.config.product_name')}</Label>
              <Input
                id="productName"
                placeholder={t('widget.config.product_name_placeholder')}
                value={config.productName}
                onChange={(e) => setConfig({...config, productName: e.target.value})}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="productUrl">{t('widget.config.product_url')}</Label>
              <Input
                id="productUrl"
                placeholder={t('widget.config.product_url_placeholder')}
                value={config.productUrl}
                onChange={(e) => setConfig({...config, productUrl: e.target.value})}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="productId">Product ID (for vote count)</Label>
              <Input
                id="productId"
                placeholder="Enter product ID to show vote count"
                value={config.productId}
                onChange={(e) => setConfig({...config, productId: e.target.value})}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="customText">{t('widget.config.custom_text')}</Label>
              <Textarea
                id="customText"
                placeholder={t('widget.config.custom_text_placeholder')}
                value={config.customText}
                onChange={(e) => setConfig({...config, customText: e.target.value})}
              />
            </div>

            <div className="space-y-4">
              <div className="space-y-2">
                <Label>{t('widget.config.theme')}</Label>
                <div className="grid grid-cols-3 gap-2">
                  {(['light', 'dark', 'gradient', 'modern', 'minimal'] as const).map((theme) => (
                    <Button
                      key={theme}
                      variant={config.theme === theme ? "default" : "outline"}
                      size="sm"
                      onClick={() => setConfig({...config, theme})}
                      className="text-xs"
                    >
                      {theme === 'light' && '☀️ Light'}
                      {theme === 'dark' && '🌙 Dark'}
                      {theme === 'gradient' && '🌈 Gradient'}
                      {theme === 'modern' && '✨ Modern'}
                      {theme === 'minimal' && '⚪ Minimal'}
                    </Button>
                  ))}
                </div>
              </div>

              <div className="space-y-2">
                <Label>{t('widget.config.size')}</Label>
                <div className="flex space-x-2">
                  {(['small', 'medium', 'large'] as const).map((size) => (
                    <Button
                      key={size}
                      variant={config.size === size ? "default" : "outline"}
                      size="sm"
                      onClick={() => setConfig({...config, size})}
                    >
                      {size === 'small' && 'S'}
                      {size === 'medium' && 'M'}
                      {size === 'large' && 'L'}
                    </Button>
                  ))}
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="showLogo"
                  checked={config.showLogo}
                  onChange={(e) => setConfig({...config, showLogo: e.target.checked})}
                  className="rounded"
                />
                <Label htmlFor="showLogo">{t('widget.config.show_logo')}</Label>
              </div>

              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="showVotes"
                  checked={config.showVotes}
                  onChange={(e) => setConfig({...config, showVotes: e.target.checked})}
                  className="rounded"
                />
                <Label htmlFor="showVotes">{t('widget.config.show_votes')}</Label>
              </div>
            </div>
          </div>

          {/* Preview and Code */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">{t('widget.preview')}</h3>
              {loading && (
                <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                  <div className="w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
                  <span>Loading votes...</span>
                </div>
              )}
            </div>

            <Card>
              <CardContent className="p-6 flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 min-h-[120px]">
                {previewWidget()}
              </CardContent>
            </Card>

            <Separator />

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label>{t('widget.code')}</Label>
                <div className="flex space-x-2">
                  <Button size="sm" variant="outline" onClick={copyToClipboard}>
                    <Copy className="w-4 h-4 mr-1" />
                    {t('widget.copy')}
                  </Button>
                  <Button size="sm" variant="outline" onClick={downloadCode}>
                    <Download className="w-4 h-4 mr-1" />
                    {t('widget.download')}
                  </Button>
                </div>
              </div>
              <Textarea
                value={generatedCode}
                readOnly
                className="font-mono text-sm h-40"
              />
            </div>

            <div className="bg-muted p-4 rounded-lg">
              <h4 className="font-medium mb-2">{t('widget.instructions.title')}</h4>
              <ol className="text-sm text-muted-foreground space-y-1 list-decimal list-inside">
                <li>{t('widget.instructions.step1')}</li>
                <li>{t('widget.instructions.step2')}</li>
                <li>{t('widget.instructions.step3')}</li>
                <li>{t('widget.instructions.step4')}</li>
              </ol>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default FeaturedWidget;
