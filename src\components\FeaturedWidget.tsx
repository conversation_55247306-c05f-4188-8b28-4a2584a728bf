
import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Copy, ExternalLink, Code, Palette, Download } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface WidgetConfig {
  theme: 'light' | 'dark' | 'gradient';
  size: 'small' | 'medium' | 'large';
  showLogo: boolean;
  customText: string;
  productName: string;
  productUrl: string;
}

const FeaturedWidget = () => {
  const { toast } = useToast();
  const [config, setConfig] = useState<WidgetConfig>({
    theme: 'gradient',
    size: 'medium',
    showLogo: true,
    customText: '',
    productName: '',
    productUrl: ''
  });
  const [generatedCode, setGeneratedCode] = useState('');

  useEffect(() => {
    generateCode();
  }, [config]);

  const generateCode = () => {
    const { theme, size, showLogo, customText, productName, productUrl } = config;
    
    const themeClasses = {
      light: 'bg-white border border-gray-200 text-gray-900',
      dark: 'bg-gray-900 border border-gray-700 text-white',
      gradient: 'bg-gradient-to-r from-purple-500 to-pink-500 text-white border-0'
    };

    const sizeClasses = {
      small: 'px-3 py-2 text-sm',
      medium: 'px-4 py-3 text-base',
      large: 'px-6 py-4 text-lg'
    };

    const displayText = customText || `${productName} est présenté sur AYOHUB`;
    
    const htmlCode = `
<!-- Widget AYOHUB -->
<div class="ayohub-widget ${themeClasses[theme]} ${sizeClasses[size]} rounded-lg shadow-lg inline-flex items-center space-x-3 font-medium transition-all hover:shadow-xl hover:scale-105 cursor-pointer" onclick="window.open('${productUrl || 'https://ayohub.com'}', '_blank')">
  ${showLogo ? `
  <div class="flex-shrink-0">
    <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
      <path d="M12 2L2 7v10c0 5.55 3.84 9.74 9 11 5.16-1.26 9-5.45 9-11V7l-10-5z"/>
    </svg>
  </div>
  ` : ''}
  <span>${displayText}</span>
  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
    <path d="m9 18 6-6-6-6"/>
  </svg>
</div>

<style>
.ayohub-widget {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  transition: all 0.3s ease;
}
.ayohub-widget:hover {
  transform: translateY(-2px);
}
</style>`.trim();

    setGeneratedCode(htmlCode);
  };

  const copyToClipboard = () => {
    navigator.clipboard.writeText(generatedCode);
    toast({
      title: "Code copié !",
      description: "Le code du widget a été copié dans votre presse-papiers"
    });
  };

  const downloadCode = () => {
    const blob = new Blob([generatedCode], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'ayohub-widget.html';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const previewWidget = () => {
    const { theme, size, showLogo, customText, productName } = config;
    
    const themeStyles = {
      light: { background: 'white', color: '#1f2937', border: '1px solid #e5e7eb' },
      dark: { background: '#111827', color: 'white', border: '1px solid #374151' },
      gradient: { background: 'linear-gradient(to right, #8b5cf6, #ec4899)', color: 'white', border: 'none' }
    };

    const sizeStyles = {
      small: { padding: '8px 12px', fontSize: '14px' },
      medium: { padding: '12px 16px', fontSize: '16px' },
      large: { padding: '16px 24px', fontSize: '18px' }
    };

    const displayText = customText || `${productName} est présenté sur AYOHUB`;

    return (
      <div 
        style={{
          ...themeStyles[theme],
          ...sizeStyles[size],
          borderRadius: '8px',
          boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1)',
          display: 'inline-flex',
          alignItems: 'center',
          gap: '12px',
          fontWeight: '500',
          cursor: 'pointer',
          transition: 'all 0.3s ease',
          fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
        }}
        onMouseEnter={(e) => {
          e.currentTarget.style.transform = 'translateY(-2px)';
          e.currentTarget.style.boxShadow = '0 10px 15px -3px rgb(0 0 0 / 0.1)';
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.transform = 'translateY(0)';
          e.currentTarget.style.boxShadow = '0 4px 6px -1px rgb(0 0 0 / 0.1)';
        }}
      >
        {showLogo && (
          <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 2L2 7v10c0 5.55 3.84 9.74 9 11 5.16-1.26 9-5.45 9-11V7l-10-5z"/>
          </svg>
        )}
        <span>{displayText}</span>
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <path d="m9 18 6-6-6-6"/>
        </svg>
      </div>
    );
  };

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline" className="w-full">
          <Code className="w-4 h-4 mr-2" />
          Générer un widget "Featured on AYOHUB"
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Palette className="w-5 h-5" />
            <span>Générateur de Widget AYOHUB</span>
          </DialogTitle>
          <DialogDescription>
            Créez un widget personnalisé pour montrer que votre produit est présenté sur AYOHUB
          </DialogDescription>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Configuration */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Configuration</h3>
            
            <div className="space-y-2">
              <Label htmlFor="productName">Nom du produit</Label>
              <Input
                id="productName"
                placeholder="Mon Super Produit"
                value={config.productName}
                onChange={(e) => setConfig({...config, productName: e.target.value})}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="productUrl">URL du produit</Label>
              <Input
                id="productUrl"
                placeholder="https://monproduit.com"
                value={config.productUrl}
                onChange={(e) => setConfig({...config, productUrl: e.target.value})}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="customText">Texte personnalisé (optionnel)</Label>
              <Textarea
                id="customText"
                placeholder="Texte personnalisé pour remplacer le texte par défaut"
                value={config.customText}
                onChange={(e) => setConfig({...config, customText: e.target.value})}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Thème</Label>
                <div className="flex space-x-2">
                  {(['light', 'dark', 'gradient'] as const).map((theme) => (
                    <Button
                      key={theme}
                      variant={config.theme === theme ? "default" : "outline"}
                      size="sm"
                      onClick={() => setConfig({...config, theme})}
                    >
                      {theme === 'light' && '☀️'}
                      {theme === 'dark' && '🌙'}
                      {theme === 'gradient' && '🌈'}
                    </Button>
                  ))}
                </div>
              </div>

              <div className="space-y-2">
                <Label>Taille</Label>
                <div className="flex space-x-2">
                  {(['small', 'medium', 'large'] as const).map((size) => (
                    <Button
                      key={size}
                      variant={config.size === size ? "default" : "outline"}
                      size="sm"
                      onClick={() => setConfig({...config, size})}
                    >
                      {size === 'small' && 'S'}
                      {size === 'medium' && 'M'}
                      {size === 'large' && 'L'}
                    </Button>
                  ))}
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="showLogo"
                checked={config.showLogo}
                onChange={(e) => setConfig({...config, showLogo: e.target.checked})}
                className="rounded"
              />
              <Label htmlFor="showLogo">Afficher le logo AYOHUB</Label>
            </div>
          </div>

          {/* Aperçu et Code */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Aperçu</h3>
            
            <Card>
              <CardContent className="p-6 flex items-center justify-center bg-gray-50 min-h-[120px]">
                {previewWidget()}
              </CardContent>
            </Card>

            <Separator />

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label>Code HTML</Label>
                <div className="flex space-x-2">
                  <Button size="sm" variant="outline" onClick={copyToClipboard}>
                    <Copy className="w-4 h-4 mr-1" />
                    Copier
                  </Button>
                  <Button size="sm" variant="outline" onClick={downloadCode}>
                    <Download className="w-4 h-4 mr-1" />
                    Télécharger
                  </Button>
                </div>
              </div>
              <Textarea
                value={generatedCode}
                readOnly
                className="font-mono text-sm h-32"
              />
            </div>

            <div className="bg-muted p-4 rounded-lg">
              <h4 className="font-medium mb-2">Instructions d'utilisation</h4>
              <ol className="text-sm text-muted-foreground space-y-1 list-decimal list-inside">
                <li>Copiez le code HTML ci-dessus</li>
                <li>Collez-le dans votre site web</li>
                <li>Le widget sera cliquable et redirigera vers votre produit</li>
                <li>Personnalisez les couleurs selon votre marque</li>
              </ol>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default FeaturedWidget;
