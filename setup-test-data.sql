-- Africa Tech Spotlight - Test Data Setup
-- Run this script in your Supabase SQL Editor to add sample data for testing

-- First, let's create some sample products (you'll need to replace user_id with actual user IDs)
-- Note: You need to register users first, then update the user_id values below

-- Sample products data
INSERT INTO products (name, description, logo_url, website_url, category, country, status, tags, votes, user_id) VALUES
(
  'MoMo Pay',
  'Solution de paiement mobile révolutionnaire pour l''Afrique de l''Ouest, permettant des transactions instantanées et sécurisées.',
  'https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=100&h=100&fit=crop&crop=center',
  'https://momopay.africa',
  'Fintech',
  'Côte d''Ivoire',
  'Lancement',
  ARRAY['mobile money', 'paiements', 'fintech'],
  247,
  (SELECT id FROM auth.users LIMIT 1)
),
(
  'AgriTech Solutions',
  'Plateforme digitale connectant les agriculteurs aux marchés locaux et internationaux avec des outils de gestion intelligents.',
  'https://images.unsplash.com/photo-1574323347407-f5e1ad6d020b?w=100&h=100&fit=crop&crop=center',
  'https://agritech.africa',
  'AgriTech',
  'Kenya',
  'Beta',
  ARRAY['agriculture', 'marketplace', 'iot'],
  189,
  (SELECT id FROM auth.users LIMIT 1)
),
(
  'EduLearn Africa',
  'Application d''apprentissage adaptatif utilisant l''IA pour personnaliser l''éducation selon les besoins de chaque élève africain.',
  'https://images.unsplash.com/photo-1503676260728-1c00da094a0b?w=100&h=100&fit=crop&crop=center',
  'https://edulearn.africa',
  'EdTech',
  'Nigeria',
  'MVP',
  ARRAY['education', 'ai', 'mobile learning'],
  156,
  (SELECT id FROM auth.users LIMIT 1)
),
(
  'HealthConnect',
  'Réseau de télémédecine connectant patients et professionnels de santé dans les zones rurales africaines.',
  'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=100&h=100&fit=crop&crop=center',
  'https://healthconnect.africa',
  'HealthTech',
  'Ghana',
  'Beta',
  ARRAY['telemedicine', 'healthcare', 'rural'],
  203,
  (SELECT id FROM auth.users LIMIT 1)
),
(
  'Solar Grid',
  'Solution de micro-réseaux solaires intelligents pour l''électrification des communautés rurales africaines.',
  'https://images.unsplash.com/photo-1509391366360-2e959784a276?w=100&h=100&fit=crop&crop=center',
  'https://solargrid.africa',
  'CleanTech',
  'Rwanda',
  'Lancement',
  ARRAY['solar', 'energy', 'rural electrification'],
  178,
  (SELECT id FROM auth.users LIMIT 1)
),
(
  'LogiTrack',
  'Plateforme de gestion logistique utilisant l''IoT et l''IA pour optimiser les chaînes d''approvisionnement en Afrique.',
  'https://images.unsplash.com/photo-1586528116311-ad8dd3c8310d?w=100&h=100&fit=crop&crop=center',
  'https://logitrack.africa',
  'LogTech',
  'Maroc',
  'MVP',
  ARRAY['logistics', 'iot', 'supply chain'],
  134,
  (SELECT id FROM auth.users LIMIT 1)
);

-- Sample comments (you'll need to update user_id and product_id after creating users and products)
INSERT INTO product_comments (product_id, user_id, content, likes) VALUES
(
  (SELECT id FROM products WHERE name = 'MoMo Pay' LIMIT 1),
  (SELECT id FROM auth.users LIMIT 1),
  'Excellent produit ! J''utilise MoMo Pay depuis 6 mois et c''est révolutionnaire pour les paiements mobiles en Afrique de l''Ouest.',
  12
),
(
  (SELECT id FROM products WHERE name = 'AgriTech Solutions' LIMIT 1),
  (SELECT id FROM auth.users LIMIT 1),
  'Cette plateforme a transformé la façon dont nous vendons nos produits agricoles. Interface intuitive et support client excellent.',
  8
),
(
  (SELECT id FROM products WHERE name = 'EduLearn Africa' LIMIT 1),
  (SELECT id FROM auth.users LIMIT 1),
  'L''IA adaptative fonctionne vraiment bien. Mes enfants apprennent plus efficacement avec cette application.',
  15
);

-- Function to create an admin user (run this after registering your account)
-- Replace '<EMAIL>' with your actual email address
CREATE OR REPLACE FUNCTION setup_admin_user(admin_email TEXT)
RETURNS TEXT AS $$
DECLARE
  user_count INTEGER;
BEGIN
  -- Check if user exists
  SELECT COUNT(*) INTO user_count 
  FROM profiles 
  WHERE email = admin_email;
  
  IF user_count = 0 THEN
    RETURN 'User with email ' || admin_email || ' not found. Please register first.';
  END IF;
  
  -- Grant admin role
  UPDATE profiles 
  SET admin_role = true 
  WHERE email = admin_email;
  
  RETURN 'Admin role granted to ' || admin_email;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Usage example (uncomment and replace with your email):
-- SELECT setup_admin_user('<EMAIL>');

-- Verify data insertion
SELECT 
  'Products' as table_name, 
  COUNT(*) as record_count 
FROM products
UNION ALL
SELECT 
  'Comments' as table_name, 
  COUNT(*) as record_count 
FROM product_comments
UNION ALL
SELECT 
  'Profiles' as table_name, 
  COUNT(*) as record_count 
FROM profiles;
