# AYOHUB Admin Dashboard Setup Guide

## 🔐 Admin Access Credentials

### Default Admin Account
- **Email**: `<EMAIL>`
- **Password**: `AyohubAdmin2024!`
- **Role**: Super Admin

### Creating Additional Admin Users
1. Register a new user through the normal signup process
2. Update their profile in the database to grant admin privileges
3. Use the SQL commands provided below

## 📋 Step-by-Step Setup Instructions

### 1. Database Configuration

#### A. Create Admin User Profile
```sql
-- First, create the user account through Supabase Auth or the signup form
-- Then update their profile to grant admin privileges

UPDATE profiles 
SET 
  admin_role = true,
  role = 'admin',
  full_name = 'AYOHUB Administrator',
  bio = 'Platform Administrator'
WHERE email = '<EMAIL>';
```

#### B. Verify Admin User Setup
```sql
-- Check if admin user exists and has correct permissions
SELECT 
  id,
  email,
  full_name,
  admin_role,
  role,
  created_at
FROM profiles 
WHERE admin_role = true;
```

### 2. Row Level Security (RLS) Policies

#### A. Admin Override Policies
```sql
-- Allow admins to read all profiles
CREATE POLICY "Admins can view all profiles" ON profiles
FOR SELECT USING (
  auth.uid() IN (
    SELECT id FROM profiles WHERE admin_role = true
  )
);

-- Allow admins to update all profiles
CREATE POLICY "Admins can update all profiles" ON profiles
FOR UPDATE USING (
  auth.uid() IN (
    SELECT id FROM profiles WHERE admin_role = true
  )
);

-- Allow admins to manage all products
CREATE POLICY "Admins can manage all products" ON products
FOR ALL USING (
  auth.uid() IN (
    SELECT id FROM profiles WHERE admin_role = true
  )
);

-- Allow admins to manage boost requests
CREATE POLICY "Admins can manage boost requests" ON boost_requests
FOR ALL USING (
  auth.uid() IN (
    SELECT id FROM profiles WHERE admin_role = true
  )
);
```

### 3. Environment Variables

Add these to your `.env.local` file:
```env
# Admin Configuration
VITE_ADMIN_EMAIL=<EMAIL>
VITE_ENABLE_ADMIN_FEATURES=true
```

### 4. Access the Admin Dashboard

1. **Login Process**:
   - Go to `/auth` page
   - Sign in with admin credentials
   - You'll be redirected to `/dashboard`
   - Admin users will see an "Admin" button in the header

2. **Admin Dashboard URL**: `/admin`

3. **Admin Features**:
   - User Management
   - Product Management
   - Boost Request Management
   - Platform Statistics
   - Content Moderation

## 🛡️ Security Features

### Authentication Guards
- Admin routes are protected by `AdminRoute` component
- Non-admin users are redirected to home page
- Admin UI elements are conditionally rendered

### Permission Levels
- **Super Admin**: Full access to all features
- **Moderator**: Limited access to content management
- **Regular User**: No admin access

### Security Checks
1. User must be authenticated
2. User must have `admin_role = true` in profiles table
3. Session must be valid
4. Admin status is verified on each protected route

## 🧪 Testing Admin Access

### Test Scenarios
1. **Admin Login**: Verify admin can login and access dashboard
2. **Non-Admin Restriction**: Verify regular users cannot access admin features
3. **Admin Operations**: Test CRUD operations on users and products
4. **Security**: Test unauthorized access attempts

### Test Commands
```sql
-- Create test admin user
INSERT INTO auth.users (id, email, encrypted_password, email_confirmed_at)
VALUES (
  gen_random_uuid(),
  '<EMAIL>',
  crypt('TestAdmin123!', gen_salt('bf')),
  now()
);

-- Grant admin privileges
UPDATE profiles 
SET admin_role = true, role = 'admin'
WHERE email = '<EMAIL>';
```

## 🔧 Troubleshooting

### Common Issues

1. **Admin Button Not Showing**
   - Check if user has `admin_role = true` in profiles table
   - Verify user is properly authenticated
   - Check browser console for errors

2. **Access Denied to Admin Routes**
   - Verify RLS policies are correctly applied
   - Check if admin user exists in database
   - Ensure session is valid

3. **Database Permission Errors**
   - Review RLS policies
   - Check if admin policies are enabled
   - Verify user ID matches in auth and profiles tables

### Debug Commands
```sql
-- Check current user's admin status
SELECT 
  auth.uid() as current_user_id,
  p.admin_role,
  p.role,
  p.email
FROM profiles p 
WHERE p.id = auth.uid();

-- List all admin users
SELECT email, full_name, admin_role, created_at
FROM profiles 
WHERE admin_role = true;
```

## 📞 Support

If you encounter issues:
1. Check the browser console for JavaScript errors
2. Review the database logs in Supabase dashboard
3. Verify all environment variables are set correctly
4. Ensure the database schema is up to date

## 🔄 Regular Maintenance

### Weekly Tasks
- Review admin access logs
- Update admin user permissions as needed
- Monitor for unauthorized access attempts

### Monthly Tasks
- Audit admin user accounts
- Review and update security policies
- Test admin functionality end-to-end
