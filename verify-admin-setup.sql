-- AYOHUB Admin Setup Verification Script
-- Run this in your Supabase SQL editor to verify admin setup

-- 1. Check if admin users exist
SELECT 
  '=== ADMIN USERS ===' as info,
  NULL as email,
  NULL as full_name,
  NULL as admin_role,
  NULL as created_at

UNION ALL

SELECT 
  'Admin User' as info,
  email,
  full_name,
  CAST(admin_role as TEXT),
  CAST(created_at as TEXT)
FROM profiles 
WHERE admin_role = true

UNION ALL

SELECT 
  '=== PLATFORM STATS ===' as info,
  NULL as email,
  NULL as full_name,
  NULL as admin_role,
  NULL as created_at

UNION ALL

SELECT 
  'Total Users' as info,
  CAST(COUNT(*) as TEXT) as email,
  'All registered users' as full_name,
  NULL as admin_role,
  NULL as created_at
FROM profiles

UNION ALL

SELECT 
  'Total Products' as info,
  CAST(COUNT(*) as TEXT) as email,
  'All submitted products' as full_name,
  NULL as admin_role,
  NULL as created_at
FROM products

UNION ALL

SELECT 
  'Total Votes' as info,
  CAST(COUNT(*) as TEXT) as email,
  'All votes cast' as full_name,
  NULL as admin_role,
  NULL as created_at
FROM votes;

-- 2. Test admin functions
SELECT 
  '=== FUNCTION TESTS ===' as test_name,
  NULL as result,
  NULL as details

UNION ALL

SELECT 
  'is_admin() function' as test_name,
  CASE 
    WHEN is_admin() THEN 'PASS - You are admin'
    ELSE 'FAIL - You are not admin'
  END as result,
  'Tests if current user has admin privileges' as details

UNION ALL

SELECT 
  'Admin stats view' as test_name,
  CASE 
    WHEN EXISTS(SELECT 1 FROM admin_stats) THEN 'PASS - Stats accessible'
    ELSE 'FAIL - Stats not accessible'
  END as result,
  'Tests if admin stats view is working' as details;

-- 3. Check RLS policies
SELECT 
  '=== RLS POLICY CHECK ===' as policy_info,
  NULL as policy_name,
  NULL as table_name

UNION ALL

SELECT 
  'RLS Status' as policy_info,
  schemaname || '.' || tablename as policy_name,
  CASE 
    WHEN rowsecurity THEN 'ENABLED'
    ELSE 'DISABLED'
  END as table_name
FROM pg_tables t
JOIN pg_class c ON c.relname = t.tablename
WHERE schemaname = 'public' 
  AND tablename IN ('profiles', 'products', 'votes', 'boost_requests', 'product_comments')
ORDER BY tablename;

-- 4. Final verification message
SELECT 
  '=== SETUP VERIFICATION ===' as message,
  CASE 
    WHEN EXISTS(SELECT 1 FROM profiles WHERE admin_role = true) 
    THEN '✅ ADMIN SETUP COMPLETE'
    ELSE '❌ ADMIN SETUP INCOMPLETE'
  END as status,
  CASE 
    WHEN EXISTS(SELECT 1 FROM profiles WHERE admin_role = true) 
    THEN 'Admin users found. You can now access /admin'
    ELSE 'No admin users found. Run admin_setup.sql first'
  END as instructions;
