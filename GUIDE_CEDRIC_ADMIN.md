# 🔐 Guide Admin AYOHUB pour Cedric

## 📧 **Votre Email Admin**
`<EMAIL>`

## 🚀 **Configuration Rapide (3 étapes)**

### **Étape 1: Configuration Base de Données**
1. Ouvrez **Supabase** → **SQL Editor**
2. Co<PERSON>z et exécutez le script `database/setup_admin_cedric.sql`
3. ✅ Vous devriez voir "Configuration de base terminée!"
4. ⚠️ **Par défaut, tous les utilisateurs ont `admin_role = false` (sécurité)**

### **Étape 2: <PERSON><PERSON>er Votre Compte**
1. Allez sur `http://localhost:5173/auth`
2. Cliquez sur **"Inscription"**
3. Utilisez ces informations :
   - **Email** : `<EMAIL>`
   - **Mot de passe** : `CedricAdmin2024!` (ou votre choix)
   - **Nom** : `<PERSON><PERSON> Alleossey`

### **Étape 3: Activer Admin (OBLIGATOIRE)**
**<PERSON><PERSON> s<PERSON>, vous devez manuellement activer les privilèges admin :**
```sql
UPDATE profiles
SET admin_role = true, role = 'admin'
WHERE email = '<EMAIL>';
```
⚠️ **Cette étape est obligatoire - aucun utilisateur n'est admin par défaut**

## ✅ **Vérification**

### Test 1: Vérifier le Statut Admin
```sql
SELECT 
  email, 
  admin_role, 
  role, 
  created_at 
FROM profiles 
WHERE email = '<EMAIL>';
```

### Test 2: Connexion Admin
1. Connectez-vous avec `<EMAIL>`
2. Vous devriez voir un bouton **"Admin"** dans l'en-tête
3. Cliquez dessus pour accéder à `/admin`

## 🎯 **Accès Admin**

- **URL du tableau de bord** : `http://localhost:5173/admin`
- **Votre email** : `<EMAIL>`
- **Mot de passe** : Celui que vous avez choisi

## 🔧 **Dépannage Rapide**

### Problème : Pas de bouton Admin
```sql
-- Vérifiez votre statut
SELECT email, admin_role FROM profiles WHERE email = '<EMAIL>';

-- Si admin_role est false ou NULL :
UPDATE profiles SET admin_role = true WHERE email = '<EMAIL>';
```

### Problème : Accès refusé
1. Videz le cache du navigateur (Ctrl+Shift+R)
2. Déconnectez-vous et reconnectez-vous
3. Vérifiez que vous utilisez le bon email

## 📋 **Script de Vérification Complète**

Exécutez ceci dans Supabase pour tout vérifier :

```sql
-- Vérification complète pour Cedric
SELECT 'VÉRIFICATION ADMIN CEDRIC' as check_type;

SELECT 
  CASE 
    WHEN admin_role = true THEN '✅ Cedric est admin'
    WHEN admin_role = false THEN '❌ Cedric n''est PAS admin'
    ELSE '❓ Compte non trouvé'
  END as statut_admin,
  email,
  role,
  created_at
FROM profiles 
WHERE email = '<EMAIL>';

-- Statistiques de la plateforme
SELECT 
  (SELECT COUNT(*) FROM profiles) as total_users,
  (SELECT COUNT(*) FROM products) as total_products,
  (SELECT COUNT(*) FROM votes) as total_votes;
```

## 🎉 **Résumé**

1. ✅ Script SQL : `database/setup_admin_cedric.sql`
2. ✅ Email admin : `<EMAIL>`
3. ✅ Accès admin : `http://localhost:5173/admin`
4. ✅ Bouton Admin visible après connexion

**Votre tableau de bord admin est prêt !** 🚀

---

*Créé spécialement pour Cedric Alleossey - AYOHUB Administrator*
