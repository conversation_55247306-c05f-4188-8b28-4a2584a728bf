-- SCRIPT SIMPLE POUR CORRIGER L'ADMIN AYOHUB
-- <PERSON><PERSON><PERSON> et collez ce script dans Supabase SQL Editor

-- Étape 1: Ajouter la colonne admin_role si elle n'existe pas
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS admin_role BOOLEAN DEFAULT FALSE;

-- Étape 2: Activer RLS sur les tables principales
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;

-- Étape 3: <PERSON><PERSON><PERSON> les politiques admin de base
DROP POLICY IF EXISTS "Ad<PERSON> can view all profiles" ON profiles;
CREATE POLICY "Ad<PERSON> can view all profiles" ON profiles
FOR SELECT USING (
  auth.uid() IN (SELECT id FROM profiles WHERE admin_role = true)
);

DROP POLICY IF EXISTS "Admins can update all profiles" ON profiles;
CREATE POLICY "Admins can update all profiles" ON profiles
FOR UPDATE USING (
  auth.uid() IN (SELECT id FROM profiles WHERE admin_role = true)
);

DROP POLICY IF EXISTS "Ad<PERSON> can manage all products" ON products;
CREATE POLICY "Admins can manage all products" ON products
FOR ALL USING (
  auth.uid() IN (SELECT id FROM profiles WHERE admin_role = true)
);

-- Étape 4: Fonction simple pour vérifier si l'utilisateur est admin
CREATE OR REPLACE FUNCTION is_admin()
RETURNS BOOLEAN
LANGUAGE sql
SECURITY DEFINER
STABLE
AS $$
  SELECT EXISTS(
    SELECT 1 FROM profiles 
    WHERE id = auth.uid() AND admin_role = true
  );
$$;

-- Étape 5: Message de succès
SELECT 'Configuration admin terminée!' as status;
SELECT 'Maintenant:' as etape;
SELECT '1. Créez un <NAME_EMAIL>' as instruction;
SELECT '2. Puis exécutez: UPDATE profiles SET admin_role = true WHERE email = ''<EMAIL>'';' as instruction;
