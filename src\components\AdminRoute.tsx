import { useEffect, useState } from "react";
import { Navigate, useLocation } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";

interface AdminRouteProps {
  children: React.ReactNode;
}

const AdminRoute = ({ children }: AdminRouteProps) => {
  const { user, isLoading } = useAuth();
  const { toast } = useToast();
  const location = useLocation();
  const [isAdmin, setIsAdmin] = useState<boolean | null>(null);
  const [checkingAdmin, setCheckingAdmin] = useState(true);

  useEffect(() => {
    checkAdminStatus();
  }, [user]);

  const checkAdminStatus = async () => {
    if (!user) {
      setIsAdmin(false);
      setCheckingAdmin(false);
      return;
    }

    try {
      setCheckingAdmin(true);
      
      // Check if user has admin privileges
      const { data: profile, error } = await supabase
        .from('profiles')
        .select('admin_role, role, email')
        .eq('id', user.id)
        .single();

      if (error) {
        console.error('Error checking admin status:', error);
        setIsAdmin(false);
        toast({
          variant: "destructive",
          title: "Access Error",
          description: "Unable to verify admin privileges. Please try again."
        });
        return;
      }

      const adminStatus = profile?.admin_role === true;
      setIsAdmin(adminStatus);

      // Log admin access attempt
      if (adminStatus) {
        console.log(`Admin access granted for ${profile.email}`);
        
        // Log the admin action
        try {
          await supabase.rpc('log_admin_action', {
            action_type: 'admin_dashboard_access',
            target_type: 'dashboard',
            target_id: null,
            action_details: {
              path: location.pathname,
              timestamp: new Date().toISOString(),
              user_agent: navigator.userAgent
            }
          });
        } catch (logError) {
          console.warn('Failed to log admin action:', logError);
        }
      } else {
        console.warn(`Admin access denied for ${profile?.email || 'unknown user'}`);
        toast({
          variant: "destructive",
          title: "Access Denied",
          description: "You don't have administrator privileges to access this page."
        });
      }
    } catch (error) {
      console.error('Error in admin check:', error);
      setIsAdmin(false);
      toast({
        variant: "destructive",
        title: "Authentication Error",
        description: "Failed to verify your permissions. Please try logging in again."
      });
    } finally {
      setCheckingAdmin(false);
    }
  };

  // Show loading state while checking authentication and admin status
  if (isLoading || checkingAdmin) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-muted-foreground">Verifying admin access...</p>
        </div>
      </div>
    );
  }

  // Redirect to login if not authenticated
  if (!user) {
    return <Navigate to="/auth" state={{ from: location }} replace />;
  }

  // Redirect to home if not admin
  if (isAdmin === false) {
    return <Navigate to="/" replace />;
  }

  // Render admin content if user is admin
  return <>{children}</>;
};

export default AdminRoute;
