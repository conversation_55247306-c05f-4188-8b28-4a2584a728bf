# 🔧 Guide de Configuration Admin AYOHUB (Français)

## ❌ Problème: Erreur de Syntaxe SQL

Si vous avez une erreur de syntaxe dans le script SQL, suivez ces étapes simples :

## ✅ Solution Simple (3 étapes)

### Étape 1: Utiliser le Script Simple
1. Ouvrez votre projet Supabase
2. Allez dans **SQL Editor**
3. <PERSON><PERSON><PERSON> et collez le contenu de `database/simple_admin_setup.sql`
4. Cliquez sur **Run** (Exécuter)

### Étape 2: C<PERSON>er un Compte Admin
1. Allez sur votre site à `/auth`
2. Créez un compte avec l'email: `<EMAIL>`
3. Utilisez le mot de passe de votre choix (ex: `CedricAdmin2024!`)

### Étape 3: Activer les Privilèges Admin
1. Retournez dans **Supabase SQL Editor**
2. Exécutez cette commande simple:

```sql
UPDATE profiles
SET admin_role = true, role = 'admin'
WHERE email = '<EMAIL>';
```

## 🎉 Vérification

Après ces étapes:
1. Connectez-vous avec `<EMAIL>`
2. Vous devriez voir un bouton **"Admin"** dans l'en-tête
3. Cliquez dessus pour accéder au tableau de bord admin

## 🔍 Script de Vérification Simple

Exécutez ceci dans Supabase pour vérifier:

```sql
SELECT 
  email, 
  admin_role, 
  role,
  created_at
FROM profiles 
WHERE admin_role = true;
```

## 🚨 Dépannage

### Problème: Le bouton Admin n'apparaît pas
**Solution:**
```sql
-- Vérifiez le statut admin
SELECT email, admin_role FROM profiles WHERE email = '<EMAIL>';

-- Si admin_role est false, corrigez-le:
UPDATE profiles SET admin_role = true WHERE email = '<EMAIL>';
```

### Problème: Accès refusé à /admin
**Solutions:**
1. Vérifiez que vous êtes connecté avec le bon compte
2. Actualisez la page
3. Vérifiez la console du navigateur pour les erreurs

### Problème: Erreur de base de données
**Solution:**
1. Assurez-vous que toutes les tables existent
2. Vérifiez que RLS est activé
3. Re-exécutez le script simple

## 📝 Script Minimal (Si tout échoue)

Si vous avez encore des problèmes, exécutez juste ceci dans Supabase:

```sql
-- Ajouter la colonne admin si elle n'existe pas
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS admin_role BOOLEAN DEFAULT FALSE;

-- Créer un admin (remplacez l'UUID par l'ID de votre utilisateur)
UPDATE profiles
SET admin_role = true, role = 'admin'
WHERE email = '<EMAIL>';

-- Vérifier
SELECT email, admin_role FROM profiles WHERE admin_role = true;
```

## 🆘 Aide Supplémentaire

Si vous avez encore des problèmes:

1. **Vérifiez l'email**: Assurez-vous d'utiliser exactement `<EMAIL>`
2. **Vérifiez la table**: `SELECT * FROM profiles LIMIT 5;`
3. **Vérifiez l'utilisateur connecté**: Regardez dans l'onglet Authentication de Supabase

## ✅ Résumé Rapide

1. ✅ Exécuter `simple_admin_setup.sql`
2. ✅ Créer compte avec `<EMAIL>`
3. ✅ Exécuter `UPDATE profiles SET admin_role = true WHERE email = '<EMAIL>';`
4. ✅ Se connecter et voir le bouton Admin

**C'est tout !** 🚀

Votre tableau de bord admin sera accessible à `/admin` après connexion.
