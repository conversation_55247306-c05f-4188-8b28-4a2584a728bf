import Header from "@/components/Header";
import Footer from "@/components/Footer";
import UserProfile from "@/components/UserProfile";
import { User } from "@/types/user";

const Profile = () => {
  // Mock user data
  const currentUser: User = {
    id: "1",
    name: "<PERSON><PERSON><PERSON>",
    email: "<EMAIL>",
    avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face",
    bio: "Fondatrice d'EduLearn Africa. Passionnée par l'éducation numérique et l'innovation pédagogique en Afrique. Ex-Google, diplômée de Stanford.",
    role: "Maker",
    country: "Sénégal",
    joinedAt: "2023-06-15",
    productsLaunched: ["2"],
    productsUpvoted: ["1", "3", "4", "5"],
    followers: 1250,
    following: 890
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />
      <main className="py-16">
        <div className="container mx-auto px-4">
          <UserProfile user={currentUser} isOwnProfile={true} />
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default Profile;