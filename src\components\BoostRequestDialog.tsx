
import { useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Zap } from "lucide-react";

interface BoostRequestDialogProps {
  productId: string;
  productName: string;
  children: React.ReactNode;
}

const BoostRequestDialog = ({ productId, productName, children }: BoostRequestDialogProps) => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [open, setOpen] = useState(false);
  const [duration, setDuration] = useState("7");
  const [message, setMessage] = useState("");
  const [loading, setLoading] = useState(false);

  const handleSubmit = async () => {
    if (!user) {
      toast({
        variant: "destructive",
        title: "Connexion requise",
        description: "Vous devez être connecté pour faire une demande de boost"
      });
      return;
    }

    setLoading(true);
    try {
      const { error } = await supabase
        .from('boost_requests')
        .insert({
          user_id: user.id,
          product_id: productId,
          duration_days: parseInt(duration),
          message: message.trim() || null
        });

      if (error) throw error;

      toast({
        title: "Demande envoyée",
        description: "Votre demande de boost a été envoyée à l'équipe d'administration. Vous recevrez une réponse sous 24h."
      });

      setOpen(false);
      setMessage("");
      setDuration("7");
    } catch (error) {
      console.error('Error submitting boost request:', error);
      toast({
        variant: "destructive",
        title: "Erreur",
        description: "Impossible d'envoyer votre demande. Veuillez réessayer."
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Zap className="w-5 h-5 text-primary" />
            <span>Demander un Boost</span>
          </DialogTitle>
          <DialogDescription>
            Demandez à mettre en avant <strong>{productName}</strong> sur AYOHUB.
            Un administrateur examinera votre demande.
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="duration">Durée du boost</Label>
            <Select value={duration} onValueChange={setDuration}>
              <SelectTrigger>
                <SelectValue placeholder="Choisir la durée" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="3">3 jours</SelectItem>
                <SelectItem value="7">7 jours</SelectItem>
                <SelectItem value="14">14 jours</SelectItem>
                <SelectItem value="30">30 jours</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="message">Message pour l'équipe (optionnel)</Label>
            <Textarea
              id="message"
              placeholder="Expliquez pourquoi votre produit mérite d'être mis en avant..."
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              className="min-h-[100px]"
            />
          </div>

          <div className="bg-muted p-4 rounded-lg">
            <h4 className="font-medium mb-2">Processus d'approbation</h4>
            <ul className="text-sm text-muted-foreground space-y-1">
              <li>• Votre demande sera examinée par notre équipe</li>
              <li>• Réponse sous 24 heures</li>
              <li>• Boost gratuit après approbation</li>
              <li>• Votre produit apparaîtra en première position</li>
            </ul>
          </div>

          <div className="flex space-x-2">
            <Button 
              onClick={handleSubmit} 
              disabled={loading}
              className="flex-1"
            >
              {loading ? "Envoi..." : "Envoyer la demande"}
            </Button>
            <Button 
              variant="outline" 
              onClick={() => setOpen(false)}
              disabled={loading}
            >
              Annuler
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default BoostRequestDialog;
