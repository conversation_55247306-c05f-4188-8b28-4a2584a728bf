import { cn } from "@/lib/utils";

interface AyohubLogoProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  variant?: 'full' | 'icon' | 'text';
  showTagline?: boolean;
}

const AyohubLogo = ({ 
  className, 
  size = 'md', 
  variant = 'full',
  showTagline = false 
}: AyohubLogoProps) => {
  const sizeClasses = {
    sm: 'h-6',
    md: 'h-8',
    lg: 'h-12',
    xl: 'h-16'
  };

  const textSizeClasses = {
    sm: 'text-lg',
    md: 'text-xl',
    lg: 'text-2xl',
    xl: 'text-4xl'
  };

  const taglineSizeClasses = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base',
    xl: 'text-lg'
  };

  // Professional African-inspired logo icon
  const LogoIcon = () => (
    <div className={cn(
      "relative flex items-center justify-center rounded-xl bg-gradient-to-br from-orange-500 via-red-500 to-purple-600 shadow-lg",
      sizeClasses[size]
    )}>
      <svg 
        viewBox="0 0 24 24" 
        fill="none" 
        className="w-3/4 h-3/4 text-white"
        xmlns="http://www.w3.org/2000/svg"
      >
        {/* African continent silhouette with tech elements */}
        <path
          d="M12 2C12 2 8 3 6 6C4 9 4 12 6 15C8 18 12 19 12 19C12 19 16 18 18 15C20 12 20 9 18 6C16 3 12 2 12 2Z"
          fill="currentColor"
          opacity="0.8"
        />
        {/* Tech circuit pattern overlay */}
        <circle cx="9" cy="8" r="1" fill="currentColor" />
        <circle cx="15" cy="8" r="1" fill="currentColor" />
        <circle cx="12" cy="12" r="1.5" fill="currentColor" />
        <path
          d="M9 8L12 12L15 8M12 12L12 16"
          stroke="currentColor"
          strokeWidth="1"
          opacity="0.6"
        />
        {/* Innovation spark */}
        <path
          d="M12 4L13 6L12 8L11 6Z"
          fill="currentColor"
          opacity="0.9"
        />
      </svg>
      
      {/* Subtle glow effect */}
      <div className="absolute inset-0 rounded-xl bg-gradient-to-br from-white/20 to-transparent" />
    </div>
  );

  const LogoText = () => (
    <div className="flex flex-col">
      <span className={cn(
        "font-bold bg-gradient-to-r from-orange-500 via-red-500 to-purple-600 bg-clip-text text-transparent",
        textSizeClasses[size]
      )}>
        AYOHUB
      </span>
      {showTagline && (
        <span className={cn(
          "text-muted-foreground font-medium",
          taglineSizeClasses[size]
        )}>
          African Tech Innovation
        </span>
      )}
    </div>
  );

  if (variant === 'icon') {
    return <LogoIcon />;
  }

  if (variant === 'text') {
    return <LogoText />;
  }

  return (
    <div className={cn("flex items-center space-x-3", className)}>
      <LogoIcon />
      <LogoText />
    </div>
  );
};

export default AyohubLogo;
