-- Add maker field to products table for better performance
-- This avoids the need to join with profiles table for basic product listing

ALTER TABLE public.products ADD COLUMN maker TEXT;

-- Update existing products with maker information from profiles
UPDATE public.products 
SET maker = COALESCE(
  (SELECT full_name FROM public.profiles WHERE id = products.user_id),
  (SELECT split_part(email, '@', 1) FROM public.profiles WHERE id = products.user_id),
  'Utilisateur'
)
WHERE maker IS NULL;

-- Set default value for future inserts
ALTER TABLE public.products ALTER COLUMN maker SET DEFAULT 'Utilisateur';

-- Create a function to automatically set maker when inserting products
CREATE OR REPLACE FUNCTION public.set_product_maker()
RETURNS TRIGGER AS $$
BEGIN
  -- Set maker from profile data
  SELECT COALESCE(full_name, split_part(email, '@', 1), 'Utilisateur')
  INTO NEW.maker
  FROM public.profiles
  WHERE id = NEW.user_id;
  
  -- If no profile found, use default
  IF NEW.maker IS NULL THEN
    NEW.maker := 'Utilisateur';
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- <PERSON>reate trigger to automatically set maker on insert
CREATE TRIGGER set_product_maker_trigger
  BEFORE INSERT ON public.products
  FOR EACH ROW
  EXECUTE FUNCTION public.set_product_maker();

-- Create trigger to update maker when profile changes
CREATE OR REPLACE FUNCTION public.update_product_maker_on_profile_change()
RETURNS TRIGGER AS $$
BEGIN
  -- Update maker in products when profile full_name changes
  UPDATE public.products 
  SET maker = COALESCE(NEW.full_name, split_part(NEW.email, '@', 1), 'Utilisateur')
  WHERE user_id = NEW.id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger on profiles table
CREATE TRIGGER update_product_maker_on_profile_change_trigger
  AFTER UPDATE OF full_name ON public.profiles
  FOR EACH ROW
  EXECUTE FUNCTION public.update_product_maker_on_profile_change();
