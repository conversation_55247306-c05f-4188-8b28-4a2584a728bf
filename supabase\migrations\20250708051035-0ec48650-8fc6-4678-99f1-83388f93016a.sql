
-- Ajouter une colonne admin_role pour identifier les administrateurs
ALTER TABLE public.profiles ADD COLUMN admin_role BOOLEAN DEFAULT FALSE;

-- Créer une table pour les commentaires des produits
CREATE TABLE public.product_comments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  product_id UUID REFERENCES public.products(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  likes INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Créer une table pour les likes des commentaires
CREATE TABLE public.comment_likes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  comment_id UUID REFERENCES public.product_comments(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(comment_id, user_id)
);

-- Modifier la table boosts pour supporter l'approbation manuelle
ALTER TABLE public.boosts 
ADD COLUMN approved_by UUID REFERENCES auth.users(id),
ADD COLUMN approved_at TIMESTAMPTZ,
ALTER COLUMN status SET DEFAULT 'pending';

-- Créer une table pour les demandes de boost
CREATE TABLE public.boost_requests (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  product_id UUID REFERENCES public.products(id) ON DELETE CASCADE,
  duration_days INTEGER DEFAULT 7,
  message TEXT,
  status TEXT DEFAULT 'pending',
  approved_by UUID REFERENCES auth.users(id),
  approved_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Activer RLS sur les nouvelles tables
ALTER TABLE public.product_comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.comment_likes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.boost_requests ENABLE ROW LEVEL SECURITY;

-- Politiques RLS pour les commentaires
CREATE POLICY "Anyone can view comments" ON public.product_comments FOR SELECT USING (true);
CREATE POLICY "Authenticated users can create comments" ON public.product_comments FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own comments" ON public.product_comments FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own comments" ON public.product_comments FOR DELETE USING (auth.uid() = user_id);

-- Politiques RLS pour les likes de commentaires
CREATE POLICY "Anyone can view comment likes" ON public.comment_likes FOR SELECT USING (true);
CREATE POLICY "Authenticated users can like comments" ON public.comment_likes FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can remove own likes" ON public.comment_likes FOR DELETE USING (auth.uid() = user_id);

-- Politiques RLS pour les demandes de boost
CREATE POLICY "Users can view own boost requests" ON public.boost_requests FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can create boost requests" ON public.boost_requests FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Admins can view all boost requests" ON public.boost_requests FOR SELECT USING (
  EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND admin_role = true)
);
CREATE POLICY "Admins can update boost requests" ON public.boost_requests FOR UPDATE USING (
  EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND admin_role = true)
);

-- Triggers pour updated_at
CREATE TRIGGER product_comments_updated_at BEFORE UPDATE ON public.product_comments FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

-- Fonction pour créer un admin par défaut (à utiliser une seule fois)
CREATE OR REPLACE FUNCTION public.create_default_admin(admin_email TEXT)
RETURNS VOID AS $$
BEGIN
  UPDATE public.profiles 
  SET admin_role = true 
  WHERE email = admin_email;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
