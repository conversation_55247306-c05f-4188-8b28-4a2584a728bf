import { Product } from "@/types/product";

export const mockProducts: Product[] = [
  {
    id: "1",
    name: "MoMo Pay",
    description: "Solution de paiement mobile révolutionnaire pour l'Afrique de l'Ouest, permettant des transactions instantanées et sécurisées.",
    logo: "https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=100&h=100&fit=crop&crop=center",
    website: "https://momopay.africa",
    country: "Côte d'Ivoire",
    category: "Fintech",
    status: "Lancement",
    votes: 247,
    createdAt: "2024-01-15",
    maker: "<PERSON><PERSON><PERSON>",
    tags: ["mobile money", "paiements", "fintech"],
    hasVoted: false
  },
  {
    id: "2", 
    name: "EduLearn Africa",
    description: "Plateforme d'apprentissage en ligne adaptée aux contextes africains, avec contenu multilingue et mode hors-ligne.",
    logo: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=center",
    website: "https://edulearn.africa",
    country: "Sénégal",
    category: "EdTech",
    status: "Beta",
    votes: 189,
    createdAt: "2024-01-14",
    maker: "Aminata Diallo",
    tags: ["éducation", "e-learning", "multilingue"],
    hasVoted: true
  },
  {
    id: "3",
    name: "AgriConnect",
    description: "Marketplace connectant directement les agriculteurs aux consommateurs, avec système de livraison intégré.",
    logo: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=100&h=100&fit=crop&crop=center",
    website: "https://agriconnect.ng",
    country: "Nigeria",
    category: "AgriTech",
    status: "MVP",
    votes: 156,
    createdAt: "2024-01-13",
    maker: "Chidi Okafor",
    tags: ["agriculture", "marketplace", "livraison"],
    hasVoted: false
  },
  {
    id: "4",
    name: "HealthTrack",
    description: "Application de suivi médical pour zones rurales, fonctionnant avec des SMS et connections basiques.",
    logo: "https://images.unsplash.com/photo-**********-5c350d0d3c56?w=100&h=100&fit=crop&crop=center", 
    website: "https://healthtrack.ke",
    country: "Kenya",
    category: "HealthTech",
    status: "Beta",
    votes: 134,
    createdAt: "2024-01-12",
    maker: "Grace Wanjiku",
    tags: ["santé", "rural", "SMS"],
    hasVoted: false
  },
  {
    id: "5",
    name: "SolarPay",
    description: "Solution de financement participatif pour l'énergie solaire en Afrique, avec paiements échelonnés.",
    logo: "https://images.unsplash.com/photo-1509391366360-2e959784a276?w=100&h=100&fit=crop&crop=center",
    website: "https://solarpay.ma",
    country: "Maroc", 
    category: "CleanTech",
    status: "Lancement",
    votes: 98,
    createdAt: "2024-01-11",
    maker: "Youssef Bennani",
    tags: ["énergie solaire", "financement", "cleantech"],
    hasVoted: false
  },
  {
    id: "6",
    name: "Taxi Brousse",
    description: "Application de covoiturage longue distance spécialement conçue pour les transports inter-villes africains.",
    logo: "https://images.unsplash.com/photo-1507679799987-c73779587ccf?w=100&h=100&fit=crop&crop=center",
    website: "https://taxibrousse.bf",
    country: "Burkina Faso",
    category: "Transport",
    status: "MVP",
    votes: 87,
    createdAt: "2024-01-10",
    maker: "Fatou Ouedraogo",
    tags: ["transport", "covoiturage", "inter-villes"],
    hasVoted: true
  }
];