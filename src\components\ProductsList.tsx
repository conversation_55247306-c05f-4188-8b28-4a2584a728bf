import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import ProductCard from "./ProductCard";
import { mockProducts } from "@/data/mockProducts";
import { Product } from "@/types/product";
import { useAuth } from "@/contexts/AuthContext";
import VoteService from "@/services/voteService";
import { Shield, AlertTriangle } from "lucide-react";

const ProductsList = () => {
  const { user } = useAuth();
  const voteService = VoteService.getInstance();
  
  const [products, setProducts] = useState<Product[]>(mockProducts);
  const [filter, setFilter] = useState("all");
  const [sortBy, setSortBy] = useState("votes");
  const [searchTerm, setSearchTerm] = useState("");

  useEffect(() => {
    // Mettre à jour les votes avec les données du service
    const updatedProducts = products.map(product => ({
      ...product,
      votes: product.votes + voteService.getVoteCount(product.id)
    }));
    setProducts(updatedProducts);
  }, []);

  const handleVote = (productId: string) => {
    // Le vote est géré par le service dans ProductCard
    // Ici on peut juste mettre à jour l'affichage si nécessaire
    setProducts(prev => 
      prev.map(product => 
        product.id === productId 
          ? { ...product, votes: product.votes + voteService.getVoteCount(productId) }
          : product
      )
    );
  };

  const filteredProducts = products
    .filter(product => {
      if (filter === "all") return true;
      if (filter === "country") return true; // Implement country filter
      return product.category.toLowerCase() === filter.toLowerCase();
    })
    .filter(product => 
      product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.country.toLowerCase().includes(searchTerm.toLowerCase())
    )
    .sort((a, b) => {
      if (sortBy === "votes") return b.votes - a.votes;
      if (sortBy === "recent") return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      return 0;
    });

  return (
    <section className="py-16 bg-gradient-earth" id="products">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            <span className="bg-gradient-sunset bg-clip-text text-transparent">
              Produits du jour
            </span>
          </h2>
          <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
            Découvrez les innovations les plus votées par la communauté tech africaine
          </p>
        </div>

        {/* Alerte de sécurité */}
        {!user && (
          <Alert className="mb-8 border-orange-200 bg-orange-50">
            <AlertTriangle className="h-4 w-4 text-orange-600" />
            <AlertDescription className="text-orange-800">
              <strong>Connexion requise pour voter</strong> - Les votes sont sécurisés et limités pour éviter les abus.
            </AlertDescription>
          </Alert>
        )}

        {user && (
          <Alert className="mb-8 border-green-200 bg-green-50">
            <Shield className="h-4 w-4 text-green-600" />
            <AlertDescription className="text-green-800">
              <strong>Votes sécurisés activés</strong> - Vous avez {voteService.getRemainingDailyVotes(user.id)} votes restants aujourd'hui.
            </AlertDescription>
          </Alert>
        )}

        {/* Filters */}
        <div className="flex flex-col md:flex-row gap-4 mb-8 max-w-4xl mx-auto">
          <Input
            placeholder="Rechercher un produit, pays ou description..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="flex-1"
          />
          
          <Select value={filter} onValueChange={setFilter}>
            <SelectTrigger className="w-full md:w-48">
              <SelectValue placeholder="Catégorie" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Toutes les catégories</SelectItem>
              <SelectItem value="fintech">Fintech</SelectItem>
              <SelectItem value="edtech">EdTech</SelectItem>
              <SelectItem value="agritech">AgriTech</SelectItem>
              <SelectItem value="healthtech">HealthTech</SelectItem>
              <SelectItem value="cleantech">CleanTech</SelectItem>
              <SelectItem value="transport">Transport</SelectItem>
            </SelectContent>
          </Select>

          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className="w-full md:w-48">
              <SelectValue placeholder="Trier par" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="votes">Plus votés</SelectItem>
              <SelectItem value="recent">Plus récents</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Products Grid */}
        {filteredProducts.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 animate-fade-in">
            {filteredProducts.map((product, index) => (
              <div key={product.id} style={{ animationDelay: `${index * 0.1}s` }}>
                <ProductCard product={product} onVote={handleVote} />
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <p className="text-muted-foreground text-lg">
              Aucun produit ne correspond à vos critères de recherche.
            </p>
            <Button variant="outline" className="mt-4" onClick={() => {
              setSearchTerm("");
              setFilter("all");
            }}>
              Réinitialiser les filtres
            </Button>
          </div>
        )}

        {/* Load More */}
        {filteredProducts.length > 0 && (
          <div className="text-center mt-12">
            <Button variant="outline" size="lg">
              Charger plus de produits
            </Button>
          </div>
        )}
      </div>
    </section>
  );
};

export default ProductsList;