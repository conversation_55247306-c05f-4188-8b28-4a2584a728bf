import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import ProductCard from "./ProductCard";
import { Product } from "@/types/product";
import { useAuth } from "@/contexts/AuthContext";
import { useLanguage } from "@/contexts/LanguageContext";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import VoteService from "@/services/voteService";
import { Shield, AlertTriangle } from "lucide-react";

const ProductsList = () => {
  const { user } = useAuth();
  const { t } = useLanguage();
  const { toast } = useToast();
  const voteService = VoteService.getInstance();

  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState("all");
  const [sortBy, setSortBy] = useState("votes");
  const [searchTerm, setSearchTerm] = useState("");

  useEffect(() => {
    loadProducts();
  }, []);

  const loadProducts = async () => {
    try {
      setLoading(true);

      // First, try to fetch products with basic fields
      const { data: productsData, error } = await supabase
        .from('products')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error loading products:', error);
        toast({
          variant: "destructive",
          title: t('common.error'),
          description: "Impossible de charger les produits."
        });
        setProducts([]);
        return;
      }

      // For each product, get the maker information from profiles
      const transformedProducts: Product[] = await Promise.all(
        (productsData || []).map(async (product) => {
          let maker = 'Utilisateur';

          // Try to get maker from product.maker field first (if it exists)
          if (product.maker) {
            maker = product.maker;
          } else {
            // Fallback: get maker from profiles table
            try {
              const { data: profileData } = await supabase
                .from('profiles')
                .select('full_name, email')
                .eq('id', product.user_id)
                .single();

              if (profileData) {
                maker = profileData.full_name || profileData.email?.split('@')[0] || 'Utilisateur';
              }
            } catch (profileError) {
              console.warn('Could not fetch profile for product:', product.id);
            }
          }

          return {
            id: product.id,
            name: product.name,
            description: product.description,
            logo: product.logo_url || '',
            website: product.website_url || '',
            country: product.country,
            category: product.category,
            status: product.status as 'MVP' | 'Beta' | 'Lancement',
            votes: product.votes || 0,
            createdAt: product.created_at,
            maker: maker,
            tags: product.tags || [],
            hasVoted: false // Will be updated based on user votes
          };
        })
      );

      // Check user votes if logged in
      if (user && transformedProducts.length > 0) {
        const productIds = transformedProducts.map(p => p.id);
        const { data: userVotes } = await supabase
          .from('votes')
          .select('product_id')
          .eq('user_id', user.id)
          .in('product_id', productIds);

        const votedProductIds = new Set(userVotes?.map(v => v.product_id) || []);

        transformedProducts.forEach(product => {
          product.hasVoted = votedProductIds.has(product.id);
        });
      }

      setProducts(transformedProducts);
    } catch (error) {
      console.error('Error loading products:', error);
      setProducts([]);
      toast({
        variant: "destructive",
        title: t('common.error'),
        description: "Erreur lors du chargement des produits"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleVote = async (productId: string) => {
    if (!user) {
      toast({
        variant: "destructive",
        title: t('auth.signin'),
        description: "Vous devez être connecté pour voter"
      });
      return;
    }

    try {
      const product = products.find(p => p.id === productId);
      if (!product) return;

      if (product.hasVoted) {
        // Remove vote
        const { error } = await supabase
          .from('votes')
          .delete()
          .eq('user_id', user.id)
          .eq('product_id', productId);

        if (error) throw error;

        // Update product vote count
        await supabase
          .from('products')
          .update({ votes: Math.max(0, product.votes - 1) })
          .eq('id', productId);

        // Update local state
        setProducts(prev =>
          prev.map(p =>
            p.id === productId
              ? { ...p, votes: Math.max(0, p.votes - 1), hasVoted: false }
              : p
          )
        );
      } else {
        // Add vote
        const { error } = await supabase
          .from('votes')
          .insert({ user_id: user.id, product_id: productId });

        if (error) throw error;

        // Update product vote count
        await supabase
          .from('products')
          .update({ votes: product.votes + 1 })
          .eq('id', productId);

        // Update local state
        setProducts(prev =>
          prev.map(p =>
            p.id === productId
              ? { ...p, votes: p.votes + 1, hasVoted: true }
              : p
          )
        );
      }
    } catch (error) {
      console.error('Error voting:', error);
      toast({
        variant: "destructive",
        title: t('common.error'),
        description: "Erreur lors du vote"
      });
    }
  };

  const filteredProducts = products
    .filter(product => {
      if (filter === "all") return true;
      if (filter === "country") return true; // Implement country filter
      return product.category.toLowerCase() === filter.toLowerCase();
    })
    .filter(product => 
      product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.country.toLowerCase().includes(searchTerm.toLowerCase())
    )
    .sort((a, b) => {
      if (sortBy === "votes") return b.votes - a.votes;
      if (sortBy === "recent") return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      return 0;
    });

  return (
    <section className="py-16 bg-gradient-earth" id="products">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            <span className="bg-gradient-sunset bg-clip-text text-transparent">
              {t('products.title')}
            </span>
          </h2>
          <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
            {t('products.subtitle')}
          </p>
        </div>

        {/* Alerte de sécurité */}
        {!user && (
          <Alert className="mb-8 border-orange-200 bg-orange-50">
            <AlertTriangle className="h-4 w-4 text-orange-600" />
            <AlertDescription className="text-orange-800">
              <strong>Connexion requise pour voter</strong> - Les votes sont sécurisés et limités pour éviter les abus.
            </AlertDescription>
          </Alert>
        )}

        {user && (
          <Alert className="mb-8 border-green-200 bg-green-50">
            <Shield className="h-4 w-4 text-green-600" />
            <AlertDescription className="text-green-800">
              <strong>Votes sécurisés activés</strong> - Vous avez {voteService.getRemainingDailyVotes(user.id)} votes restants aujourd'hui.
            </AlertDescription>
          </Alert>
        )}

        {/* Filters */}
        <div className="flex flex-col md:flex-row gap-4 mb-8 max-w-4xl mx-auto">
          <Input
            placeholder={t('products.search')}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="flex-1"
          />

          <Select value={filter} onValueChange={setFilter}>
            <SelectTrigger className="w-full md:w-48">
              <SelectValue placeholder={t('products.category')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Toutes les catégories</SelectItem>
              <SelectItem value="fintech">Fintech</SelectItem>
              <SelectItem value="edtech">EdTech</SelectItem>
              <SelectItem value="agritech">AgriTech</SelectItem>
              <SelectItem value="healthtech">HealthTech</SelectItem>
              <SelectItem value="cleantech">CleanTech</SelectItem>
              <SelectItem value="transport">Transport</SelectItem>
            </SelectContent>
          </Select>

          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className="w-full md:w-48">
              <SelectValue placeholder="Trier par" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="votes">Plus votés</SelectItem>
              <SelectItem value="recent">Plus récents</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Loading State */}
        {loading ? (
          <div className="text-center py-12">
            <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-muted-foreground">{t('common.loading')}</p>
          </div>
        ) : (
          <>
            {/* Products Grid */}
            {filteredProducts.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 animate-fade-in">
                {filteredProducts.map((product, index) => (
                  <div key={product.id} style={{ animationDelay: `${index * 0.1}s` }}>
                    <ProductCard product={product} onVote={handleVote} />
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                {products.length === 0 ? (
                  <div>
                    <p className="text-muted-foreground text-lg mb-4">
                      Aucun produit n'a encore été soumis.
                    </p>
                    <p className="text-sm text-muted-foreground">
                      Soyez le premier à partager votre innovation !
                    </p>
                  </div>
                ) : (
                  <div>
                    <p className="text-muted-foreground text-lg">
                      Aucun produit ne correspond à vos critères de recherche.
                    </p>
                    <Button variant="outline" className="mt-4" onClick={() => {
                      setSearchTerm("");
                      setFilter("all");
                    }}>
                      {t('common.reset')}
                    </Button>
                  </div>
                )}
              </div>
            )}

            {/* Load More */}
            {filteredProducts.length > 0 && (
              <div className="text-center mt-12">
                <Button variant="outline" size="lg" onClick={loadProducts}>
                  Charger plus de produits
                </Button>
              </div>
            )}
          </>
        )}
      </div>
    </section>
  );
};

export default ProductsList;