// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://ngwiynlpwjrghjxwhoqe.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5nd2l5bmxwd2pyZ2hqeHdob3FlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE5NDk1MjEsImV4cCI6MjA2NzUyNTUyMX0.LvpIPZBUcRiwUpNhQVSgcjcKQFj0VxhL0Ju8OG76Qwg";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});