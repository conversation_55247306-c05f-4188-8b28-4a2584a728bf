import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/integrations/supabase/client";

interface AdminProfile {
  id: string;
  email: string;
  full_name: string;
  admin_role: boolean;
  role: string;
}

export const useAdmin = () => {
  const { user } = useAuth();
  const [isAdmin, setIsAdmin] = useState<boolean>(false);
  const [adminProfile, setAdminProfile] = useState<AdminProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    checkAdminStatus();
  }, [user]);

  const checkAdminStatus = async () => {
    if (!user) {
      setIsAdmin(false);
      setAdminProfile(null);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('id, email, full_name, admin_role, role')
        .eq('id', user.id)
        .single();

      if (profileError) {
        throw profileError;
      }

      const adminStatus = profile?.admin_role === true;
      setIsAdmin(adminStatus);
      setAdminProfile(profile);

    } catch (err: any) {
      console.error('Error checking admin status:', err);
      setError(err.message);
      setIsAdmin(false);
      setAdminProfile(null);
    } finally {
      setLoading(false);
    }
  };

  const logAdminAction = async (
    action: string,
    targetType: string,
    targetId?: string,
    details?: any
  ) => {
    if (!isAdmin) return;

    try {
      await supabase.rpc('log_admin_action', {
        action_type: action,
        target_type: targetType,
        target_id: targetId || null,
        action_details: details || {}
      });
    } catch (error) {
      console.warn('Failed to log admin action:', error);
    }
  };

  const promoteToAdmin = async (userId: string) => {
    if (!isAdmin) {
      throw new Error('Only admins can promote users');
    }

    try {
      const { data, error } = await supabase.rpc('promote_to_admin', {
        user_id: userId
      });

      if (error) throw error;

      await logAdminAction('promote_user', 'user', userId, {
        promoted_by: adminProfile?.email
      });

      return data;
    } catch (error: any) {
      console.error('Error promoting user to admin:', error);
      throw error;
    }
  };

  const revokeAdmin = async (userId: string) => {
    if (!isAdmin) {
      throw new Error('Only admins can revoke admin privileges');
    }

    try {
      const { data, error } = await supabase.rpc('revoke_admin', {
        user_id: userId
      });

      if (error) throw error;

      await logAdminAction('revoke_admin', 'user', userId, {
        revoked_by: adminProfile?.email
      });

      return data;
    } catch (error: any) {
      console.error('Error revoking admin privileges:', error);
      throw error;
    }
  };

  const getAdminStats = async () => {
    if (!isAdmin) {
      throw new Error('Only admins can access statistics');
    }

    try {
      const { data, error } = await supabase
        .from('admin_stats')
        .select('*')
        .single();

      if (error) throw error;

      return data;
    } catch (error: any) {
      console.error('Error fetching admin stats:', error);
      throw error;
    }
  };

  const getAuditLogs = async (limit: number = 50) => {
    if (!isAdmin) {
      throw new Error('Only admins can access audit logs');
    }

    try {
      const { data, error } = await supabase
        .from('admin_audit_log')
        .select(`
          id,
          action,
          target_type,
          target_id,
          details,
          created_at,
          admin:profiles!admin_id(email, full_name)
        `)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) throw error;

      return data;
    } catch (error: any) {
      console.error('Error fetching audit logs:', error);
      throw error;
    }
  };

  const getAllUsers = async () => {
    if (!isAdmin) {
      throw new Error('Only admins can access user list');
    }

    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('id, email, full_name, admin_role, role, created_at, country, bio')
        .order('created_at', { ascending: false });

      if (error) throw error;

      return data;
    } catch (error: any) {
      console.error('Error fetching users:', error);
      throw error;
    }
  };

  const getAllProducts = async () => {
    if (!isAdmin) {
      throw new Error('Only admins can access all products');
    }

    try {
      const { data, error } = await supabase
        .from('products')
        .select(`
          *,
          user:profiles!user_id(email, full_name)
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;

      return data;
    } catch (error: any) {
      console.error('Error fetching products:', error);
      throw error;
    }
  };

  const deleteProduct = async (productId: string) => {
    if (!isAdmin) {
      throw new Error('Only admins can delete products');
    }

    try {
      const { error } = await supabase
        .from('products')
        .delete()
        .eq('id', productId);

      if (error) throw error;

      await logAdminAction('delete_product', 'product', productId, {
        deleted_by: adminProfile?.email
      });

      return true;
    } catch (error: any) {
      console.error('Error deleting product:', error);
      throw error;
    }
  };

  const updateUserRole = async (userId: string, newRole: string) => {
    if (!isAdmin) {
      throw new Error('Only admins can update user roles');
    }

    try {
      const { error } = await supabase
        .from('profiles')
        .update({ role: newRole, updated_at: new Date().toISOString() })
        .eq('id', userId);

      if (error) throw error;

      await logAdminAction('update_user_role', 'user', userId, {
        new_role: newRole,
        updated_by: adminProfile?.email
      });

      return true;
    } catch (error: any) {
      console.error('Error updating user role:', error);
      throw error;
    }
  };

  return {
    isAdmin,
    adminProfile,
    loading,
    error,
    checkAdminStatus,
    logAdminAction,
    promoteToAdmin,
    revokeAdmin,
    getAdminStats,
    getAuditLogs,
    getAllUsers,
    getAllProducts,
    deleteProduct,
    updateUserRole
  };
};

export default useAdmin;
