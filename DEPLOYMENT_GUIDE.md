# Africa Tech Spotlight - Local Deployment Guide

This guide will help you set up and run the Africa Tech Spotlight platform locally for development and testing.

## Prerequisites

Before starting, ensure you have the following installed:

- **Node.js** (version 18 or higher) - [Download here](https://nodejs.org/)
- **npm** or **yarn** package manager
- **Git** - [Download here](https://git-scm.com/)
- **Supabase CLI** (optional, for advanced database management) - [Install guide](https://supabase.com/docs/guides/cli)

## Step 1: Environment Setup

### 1.1 Clone and Navigate to Project
```bash
# If you haven't already cloned the repository
git clone <your-repository-url>
cd africa-tech-spotlight-90-main
```

### 1.2 Install Dependencies
```bash
# Install all project dependencies
npm install

# Alternative: if you prefer yarn
yarn install
```

### 1.3 Environment Configuration
```bash
# Copy the example environment file
cp .env.example .env

# Edit the .env file with your configuration
# The default values should work for testing
```

## Step 2: Database Setup

### 2.1 Supabase Project Setup
The project is already configured to use a Supabase instance. The database schema includes:

- **profiles** - User profiles and admin roles
- **products** - Product submissions and details
- **votes** - User voting system
- **product_comments** - Comments on products
- **comment_likes** - Likes on comments
- **boosts** - Product boost/promotion system
- **boost_requests** - Admin-managed boost requests

### 2.2 Database Migrations
The migrations are already defined in `supabase/migrations/`. If you need to reset or apply migrations:

1. **Using Supabase Dashboard:**
   - Go to [Supabase Dashboard](https://supabase.com/dashboard)
   - Navigate to your project
   - Go to SQL Editor
   - Run the migration files manually if needed

2. **Using Supabase CLI (Advanced):**
   ```bash
   # Initialize Supabase locally (optional)
   supabase init
   
   # Link to your project
   supabase link --project-ref ngwiynlpwjrghjxwhoqe
   
   # Apply migrations
   supabase db push
   ```

## Step 3: Running the Application

### 3.1 Start Development Server
```bash
# Start the development server
npm run dev

# Alternative: using yarn
yarn dev

# If port 8080 is busy, try:
npx vite --port 3000
```

The application will be available at: **http://localhost:3000** (or the port shown in terminal)

### 3.2 Verify Installation
1. Open your browser and navigate to `http://localhost:3000` (or the port shown in terminal)
2. You should see the Africa Tech Spotlight homepage
3. Check that the language selector works (FR/EN)
4. Verify that the products section loads (may show demo data initially)
5. Check browser console for any errors (F12 → Console tab)

## Step 4: Admin Dashboard Setup

### 4.1 Create Admin User
1. **Register a new account:**
   - Go to `http://localhost:8080/auth`
   - Create a new account with your email
   - Verify your email if required

2. **Grant Admin Access:**
   - Go to [Supabase Dashboard](https://supabase.com/dashboard)
   - Navigate to your project → Table Editor → profiles
   - Find your user profile
   - Set `admin_role` to `true`

   **Alternative SQL method:**
   ```sql
   -- Replace '<EMAIL>' with your actual email
   UPDATE profiles 
   SET admin_role = true 
   WHERE email = '<EMAIL>';
   ```

### 4.2 Access Admin Dashboard
1. Log in to your account
2. Navigate to `http://localhost:3000/admin`
3. You should see the admin dashboard with:
   - User statistics
   - Product management
   - Boost request management
   - User role management

### 4.3 Quick Admin Setup
Use the provided `admin-setup.sql` script:
1. Open Supabase Dashboard → SQL Editor
2. Copy and paste the content from `admin-setup.sql`
3. Replace `'<EMAIL>'` with your actual email
4. Run the script
5. Refresh your browser and navigate to `/admin`

## Step 5: Testing Core Features

### 5.1 User Authentication
- ✅ Register new account
- ✅ Login/logout functionality
- ✅ Password reset (if configured)
- ✅ Profile management

### 5.2 Product Management
- ✅ Submit new products
- ✅ View product details
- ✅ Vote on products
- ✅ Comment on products
- ✅ Like comments

### 5.3 Admin Features
- ✅ View user statistics
- ✅ Manage boost requests
- ✅ Grant/revoke admin roles
- ✅ Monitor platform activity

### 5.4 Internationalization
- ✅ Switch between French and English
- ✅ Verify all UI elements are translated
- ✅ Check language persistence

## Step 6: Troubleshooting

### Common Issues and Solutions

#### 6.1 Database Connection Issues
**Problem:** "Failed to load products" or database errors
**Solution:**
- Verify your `.env` file has correct Supabase credentials
- Check Supabase project status in dashboard
- Ensure RLS policies are properly configured

#### 6.2 Admin Dashboard Not Accessible
**Problem:** Redirected to regular dashboard instead of admin
**Solution:**
- Verify `admin_role` is set to `true` in your profile
- Clear browser cache and cookies
- Check browser console for errors

#### 6.3 Products Not Loading
**Problem:** Empty product list or loading errors
**Solution:**
- Check browser console for API errors
- Verify database tables exist and have data
- Test with mock data fallback

#### 6.4 Authentication Issues
**Problem:** Cannot login or register
**Solution:**
- Check Supabase Auth settings
- Verify email confirmation settings
- Check browser console for errors

### 6.5 Development Commands
```bash
# Build for production
npm run build

# Preview production build
npm run preview

# Run linting
npm run lint

# Type checking
npx tsc --noEmit
```

## Step 7: Adding Test Data

### 7.1 Sample Products
You can add sample products through the UI or directly in the database:

```sql
-- Insert sample product
INSERT INTO products (name, description, logo_url, website_url, category, country, status, tags, user_id)
VALUES (
  'Sample Tech Product',
  'A revolutionary African tech innovation',
  'https://via.placeholder.com/100',
  'https://example.com',
  'Fintech',
  'Nigeria',
  'MVP',
  ARRAY['fintech', 'mobile', 'africa'],
  (SELECT id FROM auth.users LIMIT 1)
);
```

### 7.2 Sample Comments
```sql
-- Insert sample comment
INSERT INTO product_comments (product_id, user_id, content)
VALUES (
  (SELECT id FROM products LIMIT 1),
  (SELECT id FROM auth.users LIMIT 1),
  'This is an amazing product! Great work on the innovation.'
);
```

## Next Steps

Once you have the platform running locally:

1. **Customize the platform** - Modify colors, branding, and content
2. **Add more features** - Implement additional functionality as needed
3. **Deploy to production** - Use platforms like Vercel, Netlify, or your preferred hosting
4. **Set up monitoring** - Add analytics and error tracking
5. **Configure email** - Set up transactional emails for notifications

## Support

If you encounter issues:
1. Check the browser console for errors
2. Review the Supabase logs in the dashboard
3. Verify all environment variables are set correctly
4. Ensure database migrations have been applied

The platform is now ready for local development and testing!
