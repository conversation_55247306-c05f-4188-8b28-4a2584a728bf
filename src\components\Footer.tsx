const Footer = () => {
  return (
    <footer className="bg-foreground/5 border-t border-border">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Logo and Description */}
          <div className="col-span-1 md:col-span-2">
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-8 h-8 bg-gradient-sunset rounded-lg flex items-center justify-center">
                <span className="text-primary-foreground font-bold text-lg">A</span>
              </div>
              <span className="text-xl font-bold bg-gradient-sunset bg-clip-text text-transparent">
                AYOHUB
              </span>
            </div>
            <p className="text-muted-foreground mb-4 max-w-md">
              La plateforme qui met en lumière l'innovation technologique africaine. 
              Découvrez, votez et suivez les startups les plus prometteuses du continent.
            </p>
            <div className="flex space-x-4">
              <a href="#" className="text-muted-foreground hover:text-primary transition-colors">
                Twitter
              </a>
              <a href="#" className="text-muted-foreground hover:text-primary transition-colors">
                LinkedIn
              </a>
              <a href="#" className="text-muted-foreground hover:text-primary transition-colors">
                Newsletter
              </a>
            </div>
          </div>

          {/* Navigation */}
          <div>
            <h4 className="font-semibold mb-4">Navigation</h4>
            <ul className="space-y-2">
              <li><a href="#products" className="text-muted-foreground hover:text-primary transition-colors">Produits</a></li>
              <li><a href="#submit" className="text-muted-foreground hover:text-primary transition-colors">Soumettre</a></li>
              <li><a href="#about" className="text-muted-foreground hover:text-primary transition-colors">À propos</a></li>
              <li><a href="#" className="text-muted-foreground hover:text-primary transition-colors">Blog</a></li>
            </ul>
          </div>

          {/* Community */}
          <div>
            <h4 className="font-semibold mb-4">Communauté</h4>
            <ul className="space-y-2">
              <li><a href="#" className="text-muted-foreground hover:text-primary transition-colors">Discord</a></li>
              <li><a href="#" className="text-muted-foreground hover:text-primary transition-colors">Slack</a></li>
              <li><a href="#" className="text-muted-foreground hover:text-primary transition-colors">Événements</a></li>
              <li><a href="#" className="text-muted-foreground hover:text-primary transition-colors">Partenaires</a></li>
            </ul>
          </div>
        </div>

        <div className="border-t border-border mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-muted-foreground text-sm">
            © 2024 AYOHUB. Tous droits réservés.
          </p>
          <div className="flex space-x-6 mt-4 md:mt-0">
            <a href="#" className="text-muted-foreground hover:text-primary transition-colors text-sm">
              Politique de confidentialité
            </a>
            <a href="#" className="text-muted-foreground hover:text-primary transition-colors text-sm">
              Conditions d'utilisation
            </a>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;