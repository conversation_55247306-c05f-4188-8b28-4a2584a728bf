import { Link } from "react-router-dom";
import { useLanguage } from "@/contexts/LanguageContext";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import Ayohub<PERSON><PERSON> from "./AyohubLogo";
import { Twitter, Linkedin, Mail, MessageCircle, Users, Calendar, ExternalLink } from "lucide-react";

const Footer = () => {
  const { t } = useLanguage();
  const { toast } = useToast();

  const handleNewsletterSignup = (e: React.FormEvent) => {
    e.preventDefault();
    toast({
      title: "Newsletter Subscription",
      description: "Thank you for subscribing! We'll keep you updated on African tech innovations."
    });
  };

  const handleSocialClick = (platform: string) => {
    toast({
      title: "Coming Soon",
      description: `Follow us on ${platform} - link will be available soon!`
    });
  };

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <footer className="bg-foreground/5 border-t border-border">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Logo and Description */}
          <div className="col-span-1 md:col-span-2">
            <div className="mb-4">
              <AyohubLogo size="lg" showTagline />
            </div>
            <p className="text-muted-foreground mb-6 max-w-md">
              {t('footer.description')}
            </p>

            {/* Newsletter Signup */}
            <div className="mb-6">
              <h4 className="font-semibold mb-3">{t('footer.newsletter.title')}</h4>
              <form onSubmit={handleNewsletterSignup} className="flex gap-2 max-w-sm">
                <Input
                  type="email"
                  placeholder={t('footer.newsletter.placeholder')}
                  className="flex-1"
                />
                <Button type="submit" size="sm">
                  <Mail className="w-4 h-4" />
                </Button>
              </form>
            </div>

            {/* Social Media */}
            <div className="flex space-x-4">
              <button
                onClick={() => handleSocialClick('Twitter')}
                className="text-muted-foreground hover:text-primary transition-colors p-2 hover:bg-muted rounded-lg"
                title="Follow us on Twitter"
              >
                <Twitter className="w-5 h-5" />
              </button>
              <button
                onClick={() => handleSocialClick('LinkedIn')}
                className="text-muted-foreground hover:text-primary transition-colors p-2 hover:bg-muted rounded-lg"
                title="Connect on LinkedIn"
              >
                <Linkedin className="w-5 h-5" />
              </button>
              <button
                onClick={() => handleSocialClick('Discord')}
                className="text-muted-foreground hover:text-primary transition-colors p-2 hover:bg-muted rounded-lg"
                title="Join our Discord"
              >
                <MessageCircle className="w-5 h-5" />
              </button>
            </div>
          </div>

          {/* Navigation */}
          <div>
            <h4 className="font-semibold mb-4">{t('footer.navigation.title')}</h4>
            <ul className="space-y-3">
              <li>
                <button
                  onClick={() => scrollToSection('products')}
                  className="text-muted-foreground hover:text-primary transition-colors flex items-center gap-2"
                >
                  {t('nav.products')}
                </button>
              </li>
              <li>
                <button
                  onClick={() => scrollToSection('submit')}
                  className="text-muted-foreground hover:text-primary transition-colors flex items-center gap-2"
                >
                  {t('hero.submit')}
                </button>
              </li>
              <li>
                <Link to="/about" className="text-muted-foreground hover:text-primary transition-colors flex items-center gap-2">
                  {t('nav.about')}
                </Link>
              </li>
              <li>
                <button
                  onClick={() => toast({ title: "Coming Soon", description: "Blog section will be available soon!" })}
                  className="text-muted-foreground hover:text-primary transition-colors flex items-center gap-2"
                >
                  {t('footer.navigation.blog')}
                  <ExternalLink className="w-3 h-3" />
                </button>
              </li>
            </ul>
          </div>

          {/* Community */}
          <div>
            <h4 className="font-semibold mb-4">{t('footer.community.title')}</h4>
            <ul className="space-y-3">
              <li>
                <button
                  onClick={() => handleSocialClick('Discord')}
                  className="text-muted-foreground hover:text-primary transition-colors flex items-center gap-2"
                >
                  <MessageCircle className="w-4 h-4" />
                  {t('footer.community.discord')}
                </button>
              </li>
              <li>
                <button
                  onClick={() => handleSocialClick('Slack')}
                  className="text-muted-foreground hover:text-primary transition-colors flex items-center gap-2"
                >
                  <Users className="w-4 h-4" />
                  {t('footer.community.slack')}
                </button>
              </li>
              <li>
                <button
                  onClick={() => toast({ title: "Coming Soon", description: "Events section will be available soon!" })}
                  className="text-muted-foreground hover:text-primary transition-colors flex items-center gap-2"
                >
                  <Calendar className="w-4 h-4" />
                  {t('footer.community.events')}
                </button>
              </li>
              <li>
                <button
                  onClick={() => toast({ title: "Coming Soon", description: "Partners section will be available soon!" })}
                  className="text-muted-foreground hover:text-primary transition-colors flex items-center gap-2"
                >
                  <ExternalLink className="w-4 h-4" />
                  {t('footer.community.partners')}
                </button>
              </li>
            </ul>
          </div>
        </div>

        <div className="border-t border-border mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-muted-foreground text-sm">
            {t('footer.copyright')}
          </p>
          <div className="flex space-x-6 mt-4 md:mt-0">
            <button
              onClick={() => toast({ title: "Coming Soon", description: "Privacy Policy will be available soon!" })}
              className="text-muted-foreground hover:text-primary transition-colors text-sm"
            >
              {t('footer.legal.privacy')}
            </button>
            <button
              onClick={() => toast({ title: "Coming Soon", description: "Terms of Service will be available soon!" })}
              className="text-muted-foreground hover:text-primary transition-colors text-sm"
            >
              {t('footer.legal.terms')}
            </button>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;