
import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useAdmin } from "@/hooks/useAdmin";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { Navigate } from "react-router-dom";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Users, Package, Zap, TrendingUp, CheckCircle, XCircle } from "lucide-react";

interface AdminStats {
  totalUsers: number;
  totalProducts: number;
  pendingBoosts: number;
  totalBoosts: number;
}

interface BoostRequest {
  id: string;
  user_id: string;
  product_id: string;
  duration_days: number;
  message: string | null;
  status: string;
  created_at: string;
  profiles: { full_name: string | null; email: string | null } | null;
  products: { name: string; description: string | null } | null;
}

interface UserProfile {
  id: string;
  email: string | null;
  full_name: string | null;
  admin_role: boolean;
  created_at: string;
}

const AdminDashboard = () => {
  const { user } = useAuth();
  const {
    isAdmin,
    adminProfile,
    getAdminStats,
    getAllUsers,
    getAllProducts,
    getAuditLogs,
    logAdminAction
  } = useAdmin();
  const { toast } = useToast();
  const [isAdmin, setIsAdmin] = useState(false);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState<AdminStats>({ totalUsers: 0, totalProducts: 0, pendingBoosts: 0, totalBoosts: 0 });
  const [boostRequests, setBoostRequests] = useState<BoostRequest[]>([]);
  const [users, setUsers] = useState<UserProfile[]>([]);

  useEffect(() => {
    checkAdminStatus();
  }, [user]);

  const checkAdminStatus = async () => {
    if (!user) {
      setLoading(false);
      return;
    }

    try {
      const { data: profile } = await supabase
        .from('profiles')
        .select('admin_role')
        .eq('id', user.id)
        .single();

      if (profile?.admin_role) {
        setIsAdmin(true);
        await loadDashboardData();
      }
    } catch (error) {
      console.error('Error checking admin status:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadDashboardData = async () => {
    try {
      // Charger les statistiques
      const [usersRes, productsRes, boostsRes, pendingBoostsRes] = await Promise.all([
        supabase.from('profiles').select('id', { count: 'exact', head: true }),
        supabase.from('products').select('id', { count: 'exact', head: true }),
        supabase.from('boost_requests').select('id', { count: 'exact', head: true }),
        supabase.from('boost_requests').select('id', { count: 'exact', head: true }).eq('status', 'pending')
      ]);

      setStats({
        totalUsers: usersRes.count || 0,
        totalProducts: productsRes.count || 0,
        totalBoosts: boostsRes.count || 0,
        pendingBoosts: pendingBoostsRes.count || 0
      });

      // Charger les demandes de boost - utiliser des requêtes séparées pour éviter les erreurs de jointure
      const { data: boosts } = await supabase
        .from('boost_requests')
        .select('*')
        .order('created_at', { ascending: false });

      if (boosts) {
        // Charger séparément les profils et produits
        const boostsWithDetails = await Promise.all(
          boosts.map(async (boost) => {
            const [profileRes, productRes] = await Promise.all([
              supabase.from('profiles').select('full_name, email').eq('id', boost.user_id).single(),
              supabase.from('products').select('name, description').eq('id', boost.product_id).single()
            ]);

            return {
              ...boost,
              profiles: profileRes.data || null,
              products: productRes.data || null
            };
          })
        );

        setBoostRequests(boostsWithDetails);
      }

      // Charger les utilisateurs
      const { data: usersData } = await supabase
        .from('profiles')
        .select('*')
        .order('created_at', { ascending: false });

      setUsers(usersData || []);

    } catch (error) {
      console.error('Error loading dashboard data:', error);
      toast({
        variant: "destructive",
        title: "Erreur",
        description: "Impossible de charger les données du dashboard"
      });
    }
  };

  const handleBoostRequest = async (requestId: string, action: 'approve' | 'reject') => {
    try {
      const { error } = await supabase
        .from('boost_requests')
        .update({
          status: action === 'approve' ? 'approved' : 'rejected',
          approved_by: user?.id,
          approved_at: new Date().toISOString()
        })
        .eq('id', requestId);

      if (error) throw error;

      // Si approuvé, créer le boost effectif
      if (action === 'approve') {
        const request = boostRequests.find(r => r.id === requestId);
        if (request) {
          const expiresAt = new Date();
          expiresAt.setDate(expiresAt.getDate() + request.duration_days);

          await supabase.from('boosts').insert({
            user_id: request.user_id,
            product_id: request.product_id,
            duration_days: request.duration_days,
            status: 'active',
            approved_by: user?.id,
            expires_at: expiresAt.toISOString()
          });

          // Mettre à jour le produit
          await supabase
            .from('products')
            .update({
              is_boosted: true,
              boost_expires_at: expiresAt.toISOString()
            })
            .eq('id', request.product_id);
        }
      }

      toast({
        title: action === 'approve' ? "Demande approuvée" : "Demande rejetée",
        description: `La demande de boost a été ${action === 'approve' ? 'approuvée' : 'rejetée'} avec succès.`
      });

      await loadDashboardData();
    } catch (error) {
      console.error('Error handling boost request:', error);
      toast({
        variant: "destructive",
        title: "Erreur",
        description: "Impossible de traiter la demande"
      });
    }
  };

  const toggleAdminRole = async (userId: string, currentRole: boolean) => {
    try {
      const { error } = await supabase
        .from('profiles')
        .update({ admin_role: !currentRole })
        .eq('id', userId);

      if (error) throw error;

      toast({
        title: "Rôle mis à jour",
        description: `Le rôle administrateur a été ${!currentRole ? 'accordé' : 'retiré'}.`
      });

      await loadDashboardData();
    } catch (error) {
      console.error('Error updating admin role:', error);
      toast({
        variant: "destructive",
        title: "Erreur",
        description: "Impossible de mettre à jour le rôle"
      });
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-muted-foreground">Chargement...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return <Navigate to="/auth" replace />;
  }

  if (!isAdmin) {
    return <Navigate to="/dashboard" replace />;
  }

  return (
    <div className="min-h-screen bg-background">
      <Header />
      <main className="py-16">
        <div className="container mx-auto px-4">
          <div className="mb-8">
            <h1 className="text-3xl font-bold bg-gradient-sunset bg-clip-text text-transparent">
              Dashboard Admin
            </h1>
            <p className="text-muted-foreground mt-2">
              Gérez les utilisateurs, produits et demandes de boost
            </p>
          </div>

          {/* Statistiques */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Utilisateurs</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalUsers}</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Produits</CardTitle>
                <Package className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalProducts}</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Boosts Actifs</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalBoosts}</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Demandes en Attente</CardTitle>
                <Zap className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-orange-600">{stats.pendingBoosts}</div>
              </CardContent>
            </Card>
          </div>

          <Tabs defaultValue="boosts" className="space-y-6">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="boosts">Demandes de Boost</TabsTrigger>
              <TabsTrigger value="users">Utilisateurs</TabsTrigger>
            </TabsList>

            <TabsContent value="boosts" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Demandes de Boost</CardTitle>
                  <CardDescription>
                    Gérez les demandes de mise en avant des produits
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Produit</TableHead>
                        <TableHead>Utilisateur</TableHead>
                        <TableHead>Durée</TableHead>
                        <TableHead>Message</TableHead>
                        <TableHead>Statut</TableHead>
                        <TableHead>Date</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {boostRequests.map((request) => (
                        <TableRow key={request.id}>
                          <TableCell>
                            <div>
                              <div className="font-medium">{request.products?.name || 'Produit non disponible'}</div>
                              <div className="text-sm text-muted-foreground">
                                {request.products?.description?.substring(0, 50) || 'Description non disponible'}...
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div>
                              <div className="font-medium">{request.profiles?.full_name || 'Utilisateur'}</div>
                              <div className="text-sm text-muted-foreground">{request.profiles?.email || 'Email non disponible'}</div>
                            </div>
                          </TableCell>
                          <TableCell>{request.duration_days} jours</TableCell>
                          <TableCell>{request.message || 'Aucun message'}</TableCell>
                          <TableCell>
                            <Badge variant={
                              request.status === 'pending' ? 'default' :
                              request.status === 'approved' ? 'default' : 'destructive'
                            }>
                              {request.status}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            {new Date(request.created_at).toLocaleDateString('fr-FR')}
                          </TableCell>
                          <TableCell>
                            {request.status === 'pending' && (
                              <div className="flex space-x-2">
                                <Button
                                  size="sm"
                                  onClick={() => handleBoostRequest(request.id, 'approve')}
                                  className="bg-green-600 hover:bg-green-700"
                                >
                                  <CheckCircle className="w-4 h-4 mr-1" />
                                  Approuver
                                </Button>
                                <Button
                                  size="sm"
                                  variant="destructive"
                                  onClick={() => handleBoostRequest(request.id, 'reject')}
                                >
                                  <XCircle className="w-4 h-4 mr-1" />
                                  Rejeter
                                </Button>
                              </div>
                            )}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="users" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Gestion des Utilisateurs</CardTitle>
                  <CardDescription>
                    Gérez les comptes utilisateurs et les rôles administrateur
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Nom</TableHead>
                        <TableHead>Email</TableHead>
                        <TableHead>Rôle</TableHead>
                        <TableHead>Date d'inscription</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {users.map((user) => (
                        <TableRow key={user.id}>
                          <TableCell className="font-medium">{user.full_name || 'Non défini'}</TableCell>
                          <TableCell>{user.email || 'Email non disponible'}</TableCell>
                          <TableCell>
                            <Badge variant={user.admin_role ? 'default' : 'outline'}>
                              {user.admin_role ? 'Admin' : 'Utilisateur'}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            {new Date(user.created_at).toLocaleDateString('fr-FR')}
                          </TableCell>
                          <TableCell>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => toggleAdminRole(user.id, user.admin_role)}
                            >
                              {user.admin_role ? 'Retirer Admin' : 'Promouvoir Admin'}
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default AdminDashboard;
